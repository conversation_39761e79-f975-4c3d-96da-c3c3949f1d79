package com.doublerabbit.ext;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ExtTestService implements InitializingBean, CommandLineRunner {
    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("ExtTestService afterPropertiesSet=============");
    }

    @Override
    public void run(String... args) throws Exception {
        System.out.println("ExtTestService afterPropertiesSet=============");
    }
}
