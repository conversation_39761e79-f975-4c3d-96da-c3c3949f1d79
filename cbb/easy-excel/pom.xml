<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>easy-excel</artifactId>
	<packaging>jar</packaging>
	<version>${revision}</version>
	<parent>
		<groupId>com.doublerabbit</groupId>
		<artifactId>easy-java-parent</artifactId>
		<version>1.0.0-SNAPSHOT</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>
	<properties>
		<maven.deploy.skip>true</maven.deploy.skip>
	</properties>


	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.doublerabbit</groupId>
				<artifactId>easy-java-bom</artifactId>
				<version>1.0.0-SNAPSHOT</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>3.2.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>31.1-jre</version>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>

		<dependency>
			<groupId>com.belerweb</groupId>
			<artifactId>pinyin4j</artifactId>
			<version>2.5.1</version>
		</dependency>

		<dependency>
			<groupId>jakarta.validation</groupId>
			<artifactId>jakarta.validation-api</artifactId>
		</dependency>
	</dependencies>
	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
            	<artifactId>spring-boot-maven-plugin</artifactId>
            	<executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.8</source> <!-- depending on your project -->
					<target>1.8</target> <!-- depending on your project -->
				</configuration>
			</plugin>
			<!-- build-helper-maven-plugin, 设置多个源文件夹 -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>add-source</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>add-source</goal>
						</goals>
						<configuration>
							<sources>
								<source>${basedir}/src/client/java</source>
								<source>${basedir}/src/server/java</source>
								<source>${basedir}/src/consumer/java</source>
								<!-- 我们可以通过在这里添加多个source节点，来添加任意多个源文件夹 -->
							</sources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
					<execution>
						<id>attach-sources-mixin</id>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<classifier>mixin-sources</classifier>
							<excludes>
								<!--注意从编译结果目录开始算目录结构-->
								<exclude>**/App.*</exclude>
								<exclude>META-INF/app.properties</exclude>
								<exclude>application.yaml</exclude>
								<exclude>application-local.properties</exclude>
								<exclude>log4j2.xml</exclude>
								<exclude>spy.properties</exclude>
							</excludes>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
					<execution>
						<id>package-mixin</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<classifier>mixin</classifier>
							<excludes>
								<!--注意从编译结果目录开始算目录结构-->
								<exclude>**/App.*</exclude>
								<exclude>META-INF/app.properties</exclude>
								<exclude>application.yaml</exclude>
								<exclude>application-local.properties</exclude>
								<exclude>log4j2.xml</exclude>
								<exclude>spy.properties</exclude>
							</excludes>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>

