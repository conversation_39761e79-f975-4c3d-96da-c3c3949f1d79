package com.doublerabbit.cbb.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson.JSON;
import com.doublerabbit.cbb.excel.listener.TwinCategoryDefineListener;
import com.doublerabbit.cbb.excel.listener.TwinDataListener;
import com.doublerabbit.cbb.excel.model.CategoryDefine;
import com.doublerabbit.cbb.excel.model.DemoData;
import com.doublerabbit.cbb.excel.util.TestFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
@Slf4j
public class ExcelTest {

    @Test
    public void testExcel() throws Exception {
        System.out.println(TestFileUtil.getPath());
        String filePath = TestFileUtil.getPath() + File.separator + "孪生体导入模板.xlsx";
        EasyExcel.read(filePath, DemoData.class, new PageReadListener<DemoData>(dataList -> {
            for (DemoData data : dataList) {
                System.out.println("read record:" + JSON.toJSONString(data));
            }
        })).sheet().doRead();
    }

    /**
     * 1、读取sheet页，将分类中的属性列表给提取出来；
     * 2、将每个分类下的记录读取保存，形成总记录；
     * 3、分别读取每个分类下的导入记录，进行导入，导入时判断是否有重复，如果覆盖置为true则覆盖；
     * 4、更新导入结果（成功与失败），更新导入进度；
     * @throws Exception
     * https://blog.csdn.net/qq_42747210/article/details/113280897
     * https://blog.csdn.net/qq_43750656/article/details/126304481
     */
    @Test
    public void testExcel2() throws Exception {
        String filePath = TestFileUtil.getPath() + File.separator + "数字孪生批量导入.xlsx";
//        ExcelReader excelReader = EasyExcel.read(filePath).build();
//        Integer sheets = EasyExcel.readSheet().build().getSheetNo();
//        System.out.println(sheets);
        File file = new File(filePath);
        List<ReadSheet> res = listSheets(new FileInputStream(file));
        System.out.println(res.size());
        try (ExcelReader excelReader = EasyExcel.read(file).build()) {
            // 这里为了简单 所以注册了 同样的head 和Listener 自己使用功能必须不同的Listener
//            ReadSheet readSheet1 = EasyExcel.readSheet(1).head(CategoryDefine.class).registerReadListener(new TwinCategoryDefineListener()).build();
//            excelReader.read(readSheet1);
            ReadSheet readSheet1 = EasyExcel.readSheet(2).headRowNumber(0).registerReadListener(new TwinDataListener()).build();
            excelReader.read(readSheet1);
        }
    }

    /**
     * 获取excel中sheet页总数
     * @param inputStream
     * @return
     */
    public static List<ReadSheet> listSheets(InputStream inputStream){
        if (inputStream == null){
            throw new RuntimeException("inputStream is null");
        }
        ExcelReader build = EasyExcel.read(inputStream).build();
        List<ReadSheet> readSheets = build.excelExecutor().sheetList();
        log.info(String.valueOf(readSheets));
        return readSheets;
    }
}
