package com.doublerabbit.cbb.excel.common;

import org.junit.Test;
import org.springframework.util.DigestUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
public class DigestUtilsTest {

    @Test
    public void testGBS(){
        long start = System.currentTimeMillis();
//        String domain = "1001";
        String domain = "seewolive.gbs.com";
//        String secret = "12345678";
        String secret = "8baf93ac659eb1d017d245c294092d80";
        String orgStr = domain + ":" + start + ":" + secret;
        String hashVal = DigestUtils.md5DigestAsHex(orgStr.getBytes());
        System.out.println(start + ":" + hashVal);
    }

    @Test
    public void testUC() {
        String pwd = "mzh2110901055";
        String hashVal = DigestUtils.md5DigestAsHex(pwd.getBytes());
        System.out.println(hashVal.toLowerCase());
    }

}
