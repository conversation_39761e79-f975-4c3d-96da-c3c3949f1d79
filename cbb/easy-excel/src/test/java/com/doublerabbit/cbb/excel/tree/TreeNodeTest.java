package com.doublerabbit.cbb.excel.tree;

import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
public class TreeNodeTest {

    @Test
    public void test001(){
        Twin parent1 = new Twin(1, null, "1");
        Twin p1c1 = new Twin(11, 1, "11");
        Twin p1c2 = new Twin(12, 1, "12");
        Twin p1c3 = new Twin(13, 1, "13");
        Twin p1c11 = new Twin(111, 11, "111");
        Twin p1c12 = new Twin(112, 11, "112");
        Twin p1c21 = new Twin(121, 12, "121");
        Twin p1c22 = new Twin(122, 12, "122");
        Twin p1c211 = new Twin(1211, 121, "1211");
        Twin p1c2111 = new Twin(12111, 1211, "12111");
        List<Twin> res = Lists.newArrayList(parent1,
                                            p1c1, p1c2, p1c3,
                                            p1c11, p1c12,
                                            p1c21, p1c22,
                                            p1c211,
                                            p1c2111);
        List<TreeNode> treeNodes = TreeNodeUtils.toTree(res, Twin.class);
        System.out.println(treeNodes.size());
    }

}
