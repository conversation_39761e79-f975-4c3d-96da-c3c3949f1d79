package com.doublerabbit.cbb.excel.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
public class CommonTesst {

    @Test
    public void test001(){
        String name = "ddd123";
        System.out.println(name);
        Pattern pattern = Pattern.compile("^(?!.*?\\s{2,}|\t).*$");
//        Pattern pattern = Pattern.compile("([^^$]|^\\\\S+$)");
        Matcher matcher = pattern.matcher(name);
        System.out.println(matcher.find());
    }

    @Test
    public void test002() {
        String name = "aaaab";
        Pattern p = Pattern.compile("a*b");
        Matcher m = p.matcher(name);
        boolean b = m.matches();
        System.out.println(b);
    }

    @Test
    public void test003() {
        String name = "dfdf[em:7]";
//        Pattern p = Pattern.compile("^(?!.*?:[\\uD83C\\uDF00-\\uD83D\\uDDFF]|[\\uD83E\\uDD00-\\uD83E\\uDDFF]|[\\uD83D\\uDE00-\\uD83D\\uDE4F]|[\\uD83D\\uDE80-\\uD83D\\uDEFF]|[\\u2600-\\u26FF]\\uFE0F?|[\\u2700-\\u27BF]\\uFE0F?|\\u24C2\\uFE0F?|[\\uD83C\\uDDE6-\\uD83C\\uDDFF]{1,2}|[\\uD83C\\uDD70\\uD83C\\uDD71\\uD83C\\uDD7E\\uD83C\\uDD7F\\uD83C\\uDD8E\\uD83C\\uDD91-\\uD83C\\uDD9A]\\uFE0F?|[\\u0023\\u002A\\u0030-\\u0039]\\uFE0F?\\u20E3|[\\u2194-\\u2199\\u21A9-\\u21AA]\\uFE0F?|[\\u2B05-\\u2B07\\u2B1B\\u2B1C\\u2B50\\u2B55]\\uFE0F?|[\\u2934\\u2935]\\uFE0F?|[\\u3030\\u303D]\\uFE0F?|[\\u3297\\u3299]\\uFE0F?|[\\uD83C\\uDE01\\uD83C\\uDE02\\uD83C\\uDE1A\\uD83C\\uDE2F\\uD83C\\uDE32-\\uD83C\\uDE3A\\uD83C\\uDE50\\uD83C\\uDE51]\\uFE0F?|[\\u203C\\u2049]\\uFE0F?|[\\u25AA\\u25AB\\u25B6\\u25C0\\u25FB-\\u25FE]\\uFE0F?|[\\u00A9\\u00AE]\\uFE0F?|[\\u2122\\u2139]\\uFE0F?|\\uD83C\\uDC04\\uFE0F?|\\uD83C\\uDCCF\\uFE0F?|[\\u231A\\u231B\\u2328\\u23CF\\u23E9-\\u23F3\\u23F8-\\u23FA]\\uFE0F?).*$");
//        Pattern p = Pattern.compile("^(?!.*?[\\u0000-\\uFFFF]).*$");
        Pattern p = Pattern.compile("[\\uD83D\\uDE00-\\uD83D\\uDE4F]");
        Matcher m = p.matcher(name);
        System.out.println(m.find());
    }

    @Test
    public void test004() throws Exception{
        Long timestamp = System.currentTimeMillis();
        System.out.println(timestamp);
//        String content = "body={\"coursewareId\":\"39c3412e-f2f4-4bc9-8c55-2fe9993fe9d4\",\"type\": 0,\"expiredDay\": 30\"}&x-auth-timestamp=" + timestamp;
        String content = "body={\n" +
                "    \"coursewareId\":\"39c3412e-f2f4-4bc9-8c55-2fe9993fe9d4\",\n" +
                "    \"type\": 0,\n" +
                "    \"expiredDay\": 30\n" +
                "}&x-auth-timestamp=" + timestamp;
        String secret = "EN5_key";
        String str = HmacSHA256Util.hmacSHA256(secret,content);
        System.out.println(str);
    }

    @Test
    public void test005() throws Exception {
        String uri = "/abc/Data/tile_set.json";
        Pattern p = Pattern.compile("\\S*");
        Matcher m = p.matcher(uri);
        System.out.println(m.find());
    }

    @Test
    public void test006() throws Exception {
        String content = "/data/model/tile_set.json";
        if (content.contains(".")){
            System.out.println("is contains .");
        }
    }

//    @Test
//    public void test001(){
//        LabelCreateVo createVo = new LabelCreateVo();
//        createVo.setName("test001");
//        createVo.setLabelNameType("name");
//        createVo.setLabelStyle("style1");
//        createVo.setAnchorPosition("up");
//        Position position = new Position();
//        position.setX("1");
//        position.setY("1");
//        position.setZ("1");
//        createVo.setOffsetData(position);
//        Position position1 = new Position();
//        position1.setX("1");
//        position1.setY("1");
//        position1.setZ("1");
//        createVo.setRotateData(position1);
//        createVo.setIsFaceCam(false);
//        createVo.setIsPitch(false);
//        String pretty = JSON.toJSONString(createVo, SerializerFeature.PrettyFormat,
//                SerializerFeature.WriteDateUseDateFormat,SerializerFeature.WriteMapNullValue,
//                SerializerFeature.WriteNullListAsEmpty);
//        System.out.println(pretty);
////        System.out.println(JSON.toJSON(createVo));
//    }
//
//    @Test
//    public void test002() throws Exception {
//        String orderBy = "xx desc 123 asc";
//        PageRequestUtils.from(10, 20, orderBy);
//    }

    @Test
    public void test013() throws Exception {
        String z = "-2332469";
        Double dd = Double.parseDouble(z);
        BigDecimal d1 = BigDecimal.valueOf(dd);
        System.out.println(d1);
    }

}
