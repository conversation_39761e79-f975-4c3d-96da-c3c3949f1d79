package com.doublerabbit.cbb.excel.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
public class NameUtils {

    /**
     * 中文转拼音（大写，无音标）
     * @param chineseStr
     * @return
     */
    public static Set<String> getAllPinyin(String chineseStr) {
        //输出格式设置
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        /**
         * 输出大小写设置
         *
         * LOWERCASE:输出小写
         * UPPERCASE:输出大写
         */
        format.setCaseType(HanyuPinyinCaseType.UPPERCASE);

        /**
         * 输出音标设置
         *
         * WITH_TONE_MARK:直接用音标符（必须设置WITH_U_UNICODE，否则会抛出异常）
         * WITH_TONE_NUMBER：1-4数字表示音标
         * WITHOUT_TONE：没有音标
         */
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);

        /**
         * 特殊音标ü设置
         *
         * WITH_V：用v表示ü
         * WITH_U_AND_COLON：用"u:"表示ü
         * WITH_U_UNICODE：直接用ü
         */
        format.setVCharType(HanyuPinyinVCharType.WITH_V);

        char[] hanYuArr = chineseStr.trim().toCharArray();
        Set<String> nameSet = new LinkedHashSet<>();
        try {
            for (int i = 0, len = hanYuArr.length; i < len; i++) {
                //匹配是否是汉字
                if (Character.toString(hanYuArr[i]).matches("[\\u4E00-\\u9FA5]+")) {
                    //如果是多音字，返回多个拼音
                    String[] pys = PinyinHelper.toHanyuPinyinStringArray(hanYuArr[i], format);
                    if (nameSet.isEmpty()){
                        nameSet.addAll(Arrays.asList(pys));
                    }else{
                        Set<String> term = new HashSet<>();
                        for(String a1 : nameSet){
                            for(String a2 : pys){
                                term.add(a1+a2);
                            }
                        }
                        nameSet = term;
                    }
                } else {
                    Set<String> term = new HashSet<>();
                    for(String a : nameSet){
                        term.add(a + hanYuArr[i]);
                    }
                    nameSet = term;
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            e.printStackTrace();
        }
        return nameSet;
    }
}
