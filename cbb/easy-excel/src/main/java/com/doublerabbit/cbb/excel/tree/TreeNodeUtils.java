package com.doublerabbit.cbb.excel.tree;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 下拉和级联下拉通用生成工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class TreeNodeUtils {

    /**
     * 默认id名称
     */
    public static final String ID = "id";
    /**
     * 默认父类id名称
     */
    public static final String PARENT = "parentId";

    /**
     * 默认label名称
     */
    public static final String LABEL = "name";


    /**
     * 单层下拉结构
     *
     * @param collection 目标集合
     * @param id         节点编号字段
     * @param label      节点名字段
     * @param clazz      集合元素类型
     * @param args       需要携带的参数
     * @return 转换后的下拉结构 TreeSelect
     */
    public static <E> List<TreeNode> singleTree(Collection<E> collection, String id, String label,
                                                Class<?> clazz, String... args) {
        try {
            if (collection == null || collection.isEmpty()) {
                return null;
            }
            Field idField;
            try {
                idField = clazz.getDeclaredField(id);
            } catch (NoSuchFieldException e1) {
                idField = clazz.getSuperclass().getDeclaredField(id);
            }
            Field labelField;
            try {
                labelField = clazz.getDeclaredField(label);
            } catch (NoSuchFieldException e1) {
                labelField = clazz.getSuperclass().getDeclaredField(label);
            }
            idField.setAccessible(true);
            labelField.setAccessible(true);
            List<TreeNode> list = new ArrayList<>();
            for (E e : collection) {
                TreeNode select = new TreeNode();
                select.setId(String.valueOf(idField.get(e)));
                select.setLabel(String.valueOf(labelField.get(e)));
                list.add(select);
                dynamicData(select, e, clazz, args);
            }
            idField.setAccessible(false);
            labelField.setAccessible(false);
            return list;
        } catch (Exception e) {
            log.error("单层下拉异常：", e);
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 集合转树结构(默认树结构字段)
     *
     * @param collection 目标集合
     * @param clazz      集合元素类型
     * @param args       需要携带的参数
     * @return 转换后的树形结构 TreeNode
     */
    public static <E> List<TreeNode> toTree(@NotNull Collection<E> collection, @NotNull Class<?> clazz,
                                              String... args) {
        return toTree(collection, null, null, null, clazz, args);
    }

    /**
     * 集合转树结构(自定义名称字段)
     *
     * @param collection 目标集合
     * @param label      节点名字段
     * @param clazz      集合元素类型
     * @param args       需要携带的参数
     * @return 转换后的树形结构 TreeNode
     */
    public static <E> List<TreeNode> toTree(@NotNull Collection<E> collection, @NotEmpty String label,
                                              @NotNull Class<?> clazz, String... args) {
        return toTree(collection, null, null, label, clazz, args);
    }

    /**
     * 集合转树结构(默认父类id字段为parentId，其他自定义)
     *
     * @param collection 目标集合
     * @param id         节点编号字段
     * @param label      节点名字段
     * @param clazz      集合元素类型
     * @param args       需要携带的参数
     * @return 转换后的树形结构  TreeNode
     */
    public static <E> List<TreeNode> toTree(@NotNull Collection<E> collection, @NotEmpty String id, @NotEmpty String label,
                                            @NotNull Class<?> clazz, String... args) {
        return toTree(collection, id, null, label, clazz, args);
    }

    /**
     * 集合转树结构(自定义树结构的字段)
     *
     * @param collection 目标集合
     * @param id         节点编号字段
     * @param parent     父节点编号字段
     * @param label      节点名字段
     * @param clazz      集合元素类型
     * @param args       需要携带的参数
     * @return 转换后的树形结构  TreeNode
     */
    public static <E> List<TreeNode> toTree(@NotNull Collection<E> collection, String id, String parent, String label,
                                              @NotNull Class<?> clazz, String... args) {
        try {
            if (collection == null || collection.isEmpty()) {
                return null;
            }
            //可以默认名称
            if (StringUtils.isEmpty(id)) {
                id = ID;
            }
            if (StringUtils.isEmpty(parent)) {
                parent = PARENT;
            }
            if (StringUtils.isEmpty(label)) {
                label = LABEL;
            }
            //是对象
            return collectionObj(collection, id, parent, label, clazz, args);
        } catch (Exception e) {
            log.error("多层下拉树异常：", e);
            return null;
        }
    }


    /**
     * 集合对象的封装
     */
    private static <E> List<TreeNode> collectionObj(@NotNull Collection<E> collection, String id, String parent, String label,
                                                      @NotNull Class<?> clazz, String... args) throws NoSuchFieldException, IllegalAccessException {
        // 初始化根节点集合
        List<TreeNode> list = new ArrayList<>();
        // 获取 id 字段, 从当前对象或其父类
        Field idField;
        try {
            idField = clazz.getDeclaredField(id);
        } catch (NoSuchFieldException e1) {
            idField = clazz.getSuperclass().getDeclaredField(id);
        }
        // 获取 parentId 字段, 从当前对象或其父类
        Field parentField;
        try {
            parentField = clazz.getDeclaredField(parent);
        } catch (NoSuchFieldException e1) {
            parentField = clazz.getSuperclass().getDeclaredField(parent);
        }
        // 获取 label 字段, 从当前对象或其父类
        Field labelField;
        try {
            labelField = clazz.getDeclaredField(label);
        } catch (NoSuchFieldException e1) {
            labelField = clazz.getSuperclass().getDeclaredField(label);
        }
        idField.setAccessible(true);
        parentField.setAccessible(true);
        labelField.setAccessible(true);
        // 找出所有的根节点
        for (E e : collection) {
            Object parentId = parentField.get(e);
            if (isParentNode(parentId, idField, collection)) {
                TreeNode treeNode = new TreeNode();
                treeNode.setId(String.valueOf(idField.get(e)));
                treeNode.setLabel(String.valueOf(labelField.get(e)));
                list.add(treeNode);
                dynamicData(treeNode, e, clazz, args);
            }
        }
        // 依次添加子节点
        for (TreeNode treeNode : list) {
            addChild(treeNode, collection, idField, parentField, labelField, clazz, args);
        }
        idField.setAccessible(false);
        parentField.setAccessible(false);
        labelField.setAccessible(false);
        return list;
    }


    /**
     * 添加跟随下拉的字段
     *
     * @param treeNode 当前节点
     * @param e
     * @param clazz
     * @param args       需要跟随的字段
     * @throws IllegalAccessException
     * @throws NoSuchFieldException
     */
    private static <E> void dynamicData(@NotNull TreeNode treeNode, @NotNull Object e, @NotNull Class<E> clazz, String... args) throws IllegalAccessException, NoSuchFieldException {
        if (args.length > 0) {
            Map<String, Object> dataMap = new HashMap<>();
            for (String arg : args) {
                Field field;
                try {
                    field = clazz.getDeclaredField(arg);
                } catch (NoSuchFieldException e1) {
                    field = clazz.getSuperclass().getDeclaredField(arg);
                }
                field.setAccessible(true);
                dataMap.put(arg, field.get(e));
                field.setAccessible(false);
            }
            treeNode.setDataMap(dataMap);
        }
    }


    /**
     * 为目标节点添加孩子节点
     *
     * @param node        目标节点
     * @param collection  目标集合
     * @param idField     ID 字段
     * @param parentField 父节点字段
     * @param labelField  字节点字段
     */
    private static <E> void addChild(@NotNull TreeNode node, @NotNull Collection<E> collection,
                                     @NotNull Field idField, @NotNull Field parentField, @NotNull Field labelField,
                                     @NotNull Class<?> clazz, String... args) throws IllegalAccessException, NoSuchFieldException {
        //父节点的id
        String id = node.getId();
        //子节点集合
        List<TreeNode> children = new ArrayList<>();
        for (E e : collection) {
            String parentId = String.valueOf(parentField.get(e));
            if (id.equals(parentId)) {
                // 将当前节点添加到目标节点的孩子节点
                TreeNode treeNode = new TreeNode();
                treeNode.setId(String.valueOf(idField.get(e)));
                treeNode.setLabel(String.valueOf(labelField.get(e)));
                dynamicData(treeNode, e, clazz, args);
                children.add(treeNode);
                // 递归添加子节点
                addChild(treeNode, collection, idField, parentField, labelField, clazz, args);
            }
        }
        node.setChildren(children);
    }

    /**
     * 判断是否是根节点, 判断方式为:
     * 1、父节点编号为空或为 0, 则认为是根节点
     * 2、父节点在集合中不存在父节点
     *
     * @param parentId 父节点编号
     * @return 是否是根节点
     */
    private static <E> boolean isParentNode(Object parentId, Field idField, @NotNull Collection<E> collection) throws IllegalAccessException {
        if (parentId == null) {
            return true;
        } else if (parentId instanceof String && (StringUtils.isEmpty(String.valueOf(parentId)) || parentId.equals("0"))) {
            return true;
        } else if (parentId instanceof Long && Long.valueOf(0).equals(parentId)) {
            return true;
        } else {
            for (E e : collection) {
                Object o = idField.get(e);
                if (Objects.equals(o, parentId)) {
                    return false;
                }
            }
            return true;
        }
    }
}