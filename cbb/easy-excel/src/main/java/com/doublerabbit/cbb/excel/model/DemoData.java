package com.doublerabbit.cbb.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * 如果定义的顺序与文件中列的顺序一致的话可以不用写@ExcelProperty
 */
@Data
public class DemoData implements Serializable {

    @ExcelProperty(value = "学校编号", index = 0)
    private String groupId;

    @ExcelProperty(value = "所在建筑", index = 1)
    private String buildName;

    @ExcelProperty(value = "所在教室", index = 2)
    private String classroomName;

    @ExcelProperty(value = "设备sn", index = 3)
    private String sn;

    @ExcelProperty(value = "设备类别", index = 4)
    private String type;

}
