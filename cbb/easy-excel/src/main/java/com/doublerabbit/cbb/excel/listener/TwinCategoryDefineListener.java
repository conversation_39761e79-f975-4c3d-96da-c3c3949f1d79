package com.doublerabbit.cbb.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellExtra;
import com.doublerabbit.cbb.excel.model.CategoryDefine;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
@Slf4j
public class TwinCategoryDefineListener extends AnalysisEventListener<CategoryDefine> {

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        super.onException(exception, context);
    }

    @Override
    public void invoke(CategoryDefine data, AnalysisContext context) {
        log.info("CategoryDefine data: {}" , data);
    }

    @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        super.extra(extra, context);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 读完所有数据后做统一处理
        log.info("read all data completed");
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        return super.hasNext(context);
    }
}
