package com.doublerabbit.cbb.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
@Data
public class CategoryDefine {

    @ExcelProperty(value = "分类名称[*]", index = 0)
    private String name;

    @ExcelProperty(value = "属性名称[*]", index = 1)
    private String propertyName;

    @ExcelProperty(value = "类型[*]", index = 2)
    private String dataType;

    @ExcelProperty(value = "是否必填", index = 3)
    private String required;

    @ExcelProperty(value = "默认值", index = 4)
    private String defaultValue;

    @ExcelProperty(value = "属性描述", index = 5)
    private String description;
}
