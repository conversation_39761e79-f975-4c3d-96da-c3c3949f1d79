package com.doublerabbit.cbb.excel.tree;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * TreeNode树结构实体类
 *
 * <AUTHOR>
 */
@Data
public class TreeNode {

    /**
     * 节点ID
     */
    private String id;

    /**
     * 节点名称
     */
    private String label;

    /**
     * 下拉节点中携带数据
     */
    private Map<String, Object> dataMap;


    /**
     * 子节点
     */
    private List<TreeNode> children;


    public TreeNode() {}

}