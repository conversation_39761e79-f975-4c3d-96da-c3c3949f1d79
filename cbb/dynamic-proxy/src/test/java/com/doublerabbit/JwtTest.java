package com.doublerabbit;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import org.junit.Test;
import org.springframework.util.DigestUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
public class JwtTest {

    private static final String SECRET_KEY_TOKEN = "da kindlink";

    @Test
    public void testGenerateJwt() throws Exception {
        Long lifeCycle = System.currentTimeMillis() + 3600 * 1000L;
        String token = JWT.create()
                .withClaim("organize", "d5sb")
                .withExpiresAt(new Date(lifeCycle))
                .sign(Algorithm.HMAC256(DigestUtils.md5DigestAsHex(SECRET_KEY_TOKEN.getBytes())));
        System.out.println(token);
    }

}
