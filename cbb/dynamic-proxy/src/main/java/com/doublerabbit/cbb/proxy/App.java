package com.doublerabbit.cbb.proxy;

import com.doublerabbit.cbb.proxy.api.TestService;
import com.doublerabbit.cbb.proxy.api.UserService;
import com.doublerabbit.cbb.proxy.cglib.UcService;
import com.doublerabbit.cbb.proxy.validate.DwsContext;
import com.doublerabbit.cbb.proxy.validate.DwsContextBuilder;
import com.doublerabbit.cbb.proxy.validate.DwsContextService;
import com.doublerabbit.cbb.proxy.validate.TwinValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Hello world!
 *
 */
@SpringBootApplication
public class App implements CommandLineRunner
{

    @Autowired
    private UserService userService;

    @Autowired
    private TestService testService;

    @Autowired
    private UcService ucService;

    @Autowired
    private DwsContextService contextService;

    @Autowired
    private TwinValidator twinValidator;

    public static void main( String[] args )
    {
        System.out.println( "Hello World!" );
        SpringApplication.run(App.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        userService.save("hello user");
        testService.test("hello test");
        userService.say("oh yes=====");
        System.out.println(ucService.getUserName(10000L));

        // 构造校验
        DwsContext context = DwsContextBuilder.create().buildValid();
        contextService.runWithinContext(context, () -> {
            twinValidator.validate();
        });
    }
}
