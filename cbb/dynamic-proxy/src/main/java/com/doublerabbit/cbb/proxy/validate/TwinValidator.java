package com.doublerabbit.cbb.proxy.validate;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
@Service
public class TwinValidator {

    @Autowired
    private DwsContextService contextService;

    @Autowired
    private TwinCategoryValidator twinCategoryValidator;

    public void validate(){
        // 先校验孪生体类
        String twinCategoryName = "园区";
        contextService.getContext().set("twinCategoryName", twinCategoryName);
        twinCategoryValidator.validate();
    }

}
