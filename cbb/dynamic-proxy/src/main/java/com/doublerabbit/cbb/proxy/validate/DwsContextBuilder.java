package com.doublerabbit.cbb.proxy.validate;

import com.google.common.collect.Maps;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class DwsContextBuilder {
    private final Map<String, Object> attrs = Maps.newConcurrentMap();

    public DwsContextBuilder() {
    }

    public static DwsContextBuilder create() {
        return new DwsContextBuilder();
    }

    public static DwsContextBuilder create(DwsContext origin) {
        return create().withAttrs(origin.asMap());
    }

    public DwsContextBuilder withAttrs(Map<String, Object> attrs) {
        for (Map.Entry<String, Object> o : attrs.entrySet()) {
            this.putAttr(o.getKey(), o.getValue());
        }
        return this;
    }

    public DwsContextBuilder withAttr(String key, Object value) {
        this.putAttr(key, value);
        return this;
    }
    private void putAttr(String key, Object value) {
        if (value != null) {
            this.attrs.put(key, value);
        }
    }

    public DwsContext buildValid() {
        return this.buildValid(new EmptyDwsContext());
    }

    public DwsContext buildValid(DwsContext parent) {
        Assert.notNull(parent, "Parent context cannot be null!");
        return this.build(parent);
    }

    public DwsContext build() {
        return this.build(new EmptyDwsContext());
    }

    public DwsContext build(DwsContext parent) {
        return new DwsContextImpl(parent, this.attrs);
    }

    private static final class EmptyDwsContext implements DwsContext {

        private final Map<String, Object> attributes;

        private EmptyDwsContext() {
            this.attributes = Maps.newConcurrentMap();
        }

        public Object get(String attributeName) {
            return this.attributes.get(attributeName);
        }

        @Override
        public void set(String attributeName, Object object) {
            this.attributes.put(attributeName, object);
        }

        public Map<String, Object> asMap() {
            return this.attributes;
        }
    }

    private static final class DwsContextImpl implements DwsContext {
        private final DwsContext        context;
        private final Map<String, Object> attributes;

        private DwsContextImpl(DwsContext context, Map<String, Object> attributes) {
            this.attributes = Maps.newConcurrentMap();
            this.context = context;
            this.attributes.putAll(attributes);
        }

        public Object get(String attributeName) {
            return this.attributes.containsKey(attributeName) ?
                    this.attributes.get(attributeName) : this.context.get(attributeName);
        }

        @Override
        public void set(String attributeName, Object object) {
            this.attributes.put(attributeName, object);
        }

        public Map<String, Object> asMap() {
            HashMap<String, Object> var1 = Maps.newHashMap(this.context.asMap());
            var1.putAll(this.attributes);
            return Collections.unmodifiableMap(var1);
        }
    }
}
