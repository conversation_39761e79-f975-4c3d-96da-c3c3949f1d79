package com.doublerabbit.cbb.proxy.validate;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
@Service
public class TwinCategoryValidator {

    @Autowired
    private DwsContextService contextService;

    public void validate(){
        System.out.println(contextService.get("twinCategoryName"));
        // 校验
    }

}
