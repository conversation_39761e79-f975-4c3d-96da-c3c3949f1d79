package com.doublerabbit.cbb.proxy.validate;

import org.springframework.core.NamedThreadLocal;
import org.springframework.stereotype.Service;

import java.util.concurrent.Callable;

@Service
public class DwsContextServiceImpl implements DwsContextService {
    private final ThreadLocal<DwsContext> currentContext = new NamedThreadLocal<>("dwsLocalContext");

    @Override
    public DwsContext getContext() {
        DwsContext context = this.current();
        if (context == null) {
            throw new IllegalStateException("Not within any current!");
        } else {
            return context;
        }
    }

    @Override
    public Object get(String attributeName) {
        return this.getContext().get(attributeName);
    }

    @Override
    public void runWithinContext(DwsContext context, Runnable task) {
        try {
            this.callWithinContext(context, new CallableRunnableWrapper(task));
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public <V> V callWithinContext(DwsContext context, Callable<V> task) throws Exception {
        DwsContext parent = this.current();
        DwsContext child = buildContext(parent, context);
        this.currentContext.set(child);
        V actual;
        try {
            actual = task.call();
        } finally {
            this.updateContext(parent, child);
        }
        return actual;
    }

    private DwsContext current() {
        return currentContext.get();
    }

    private void updateContext(DwsContext parentContext, DwsContext context) {
        if (parentContext == null) {
            this.currentContext.remove();
        } else {
            this.currentContext.set(parentContext);
        }
    }

    private static DwsContext buildContext(DwsContext parentContext, DwsContext context) {
        DwsContextBuilder builder = DwsContextBuilder.create(context);
        return parentContext == null ?
                builder.buildValid() :
                builder.buildValid(parentContext);
    }
}
