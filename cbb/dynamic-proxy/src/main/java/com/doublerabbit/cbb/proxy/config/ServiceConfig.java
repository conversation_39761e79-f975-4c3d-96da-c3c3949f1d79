package com.doublerabbit.cbb.proxy.config;

import com.doublerabbit.cbb.proxy.api.TestService;
import com.doublerabbit.cbb.proxy.api.UserService;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
@Configuration
public class ServiceConfig {

    @Bean
    public UserService userService() throws Exception {
        ProxyFactoryBean<UserService> proxy = new ProxyFactoryBean(UserService.class);
        return proxy.getObject();
    }

    @Bean
    public TestService testService() throws Exception {
        ProxyFactoryBean<TestService> proxy = new ProxyFactoryBean(TestService.class);
        return proxy.getObject();
    }

    private class ProxyFactoryBean<T> implements FactoryBean{

        private Class<T> interfaceClass;

        public ProxyFactoryBean(Class<T> interfaceClass){
            this.interfaceClass = interfaceClass;
        }

        @Override
        public T getObject() throws Exception {
            return (T)Proxy.newProxyInstance(interfaceClass.getClassLoader(), new Class[]{interfaceClass},
                    new InvocationHandler() {
                        @Override
                        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                            //实现业务逻辑,比如发起网络连接，执行远程调用，获取到结果，并返回
                            System.out.println(method.getName() + " method invoked ! param: "+ Arrays.toString(args));
                            return null;
                        }
                    });
        }

        @Override
        public Class<?> getObjectType() {
            return interfaceClass;
        }
    }

}
