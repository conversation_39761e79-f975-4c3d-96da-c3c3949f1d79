package com.doublerabbit.cbb.proxy.validate;

import java.util.concurrent.Callable;

public class CallableRunnableWrapper implements Callable<Void> {

    private final Runnable task;

    public CallableRunnableWrapper(Runnable task) {
        this.task = task;
    }

    @Override
    public Void call() throws Exception {
        this.task.run();
        return null;
    }

    public Runnable getTask() {
        return task;
    }
}
