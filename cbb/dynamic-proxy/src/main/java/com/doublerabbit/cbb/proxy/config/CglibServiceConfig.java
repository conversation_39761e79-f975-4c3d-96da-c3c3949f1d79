package com.doublerabbit.cbb.proxy.config;

import com.doublerabbit.cbb.proxy.cglib.UcService;
import net.sf.cglib.proxy.Enhancer;
import net.sf.cglib.proxy.MethodInterceptor;
import net.sf.cglib.proxy.MethodProxy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
@Configuration
public class CglibServiceConfig {

    @Bean
    public UcService ucService() throws Exception {
        CglibInterceptor proxy = new CglibInterceptor(new UcService());
        return (UcService) proxy.getTargetProxy();
    }

    private class CglibInterceptor implements MethodInterceptor{

        private Object taget;

        public CglibInterceptor(Object taget) {
            this.taget = taget;
        }

        @Override
        public Object intercept(Object o, Method method, Object[] objects, MethodProxy methodProxy) throws Throwable {
            System.out.println("开启事务..." + taget.getClass().getSimpleName());
            Object objValue = null;
            try {
                // 反射调用目标类方法
                objValue = method.invoke(taget, objects);
                System.out.println("返回值为：" + objValue);
            } catch (Exception e) {
                System.out.println("调用异常!" + e.getMessage());
            } finally {
                System.out.println("调用结束，关闭事务...");
            }
            return objValue;
        }

        public Object getTargetProxy() {
            // Enhancer类是cglib中的一个字节码增强器，它可以方便的为你所要处理的类进行扩展
            Enhancer eh = new Enhancer();
            // 1.将目标对象所在的类作为Enhancer类的父类
            eh.setSuperclass(taget.getClass());
            // 2.通过实现MethodInterceptor实现方法回调
            eh.setCallback(this);
            // 3. 创建代理实例
            return eh.create();
        }
    }

}
