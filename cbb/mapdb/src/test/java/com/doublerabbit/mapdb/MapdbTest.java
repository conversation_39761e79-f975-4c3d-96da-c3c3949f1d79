package com.doublerabbit.mapdb;

import org.jetbrains.annotations.NotNull;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mapdb.DB;
import org.mapdb.DBMaker;
import org.mapdb.DataInput2;
import org.mapdb.Serializer;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

public class MapdbTest {

    private static DB db;

    @BeforeClass
    public static void before(){
        db = DBMaker.fileDB("file.db").make();
    }

    @AfterClass
    public static void after(){
        if (Objects.nonNull(db)){
            db.close();
        }
    }

    @Test
    public void testMap(){
        ConcurrentMap<String, Long> map = db.hashMap("map", Serializer.STRING, Serializer.LONG).createOrOpen();
        map.put("hello", 123L);
        System.out.println(map.get("hello"));
    }

    @Test
    public void delay(){
        DelayQueue<TaskDelay> delayQueue = new DelayQueue<>();
        delayQueue.put(new TaskDelay(20*1000L));
        delayQueue.put(new TaskDelay(50*1000L));
        System.out.println(new Date());

        try {
            while (true){
                System.out.println(new Date()+">>>>>");
                TaskDelay delay = delayQueue.take();
                delay.execute();
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    class TaskDelay implements Delayed{

        private Long startTime;
        private long expire;

        public TaskDelay(long delayTime){
            this.startTime = delayTime;
            this.expire = System.currentTimeMillis() + startTime;
        }

        @Override
        public long getDelay(@NotNull TimeUnit unit) {
            return unit.convert(expire - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
        }

        @Override
        public int compareTo(@NotNull Delayed o) {
            return Long.compare(this.startTime, ((TaskDelay) o).startTime);
        }

        public void execute(){
            System.out.println("hello======");
        }
    }

}
