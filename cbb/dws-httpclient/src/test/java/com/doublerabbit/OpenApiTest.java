package com.doublerabbit;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

//import com.seewo.open.sdk.util.SignUtils;

public class OpenApiTest {

//    String appId = "c9635d7758e940358d802e163da59eda";
    String appId = "c9ff5725f392436fbba697db7b3481e6";
//    String secret = "0WcWzILfuXPSITTjohUomuwd2TMQWofD";
    String secret = "yZRMAmFEYLS2jPuSYVyN38j1syl4AdBM";
    String signMethod = "md5";

//    @Test
//    public void test001(){
//        String content = "{\n" +
//                "    \"schoolCode\":\"2PNB\",\n" +
//                "    \"startTime\":\"1696936571937\",\n" +
//                "    \"endTime\":\"1696937581937\"\n" +
//                "}";
//        String content2 = "{\n" +
//                "\t\"pageSize\":10,\n" +
//                "\t\"startTime\":1691734966404,\n" +
//                "\t\"endTime\":1697005366404,\n" +
//                "\t\"page\":1,\n" +
//                "\t\"deviceSn\":\"DSSV20E050638917C00005\",\n" +
//                "\t\"schoolCode\":\"2PNB\",\n" +
//                "\t\"queryType\":2\n" +
//                "}";
//        String content3 = "{\"schoolCode\":\"2PNB\",\"startTime\":1697005366000,\"endTime\":1691734966000,\"page\":1,\"pageSize\":10,\"queryType\":2,\"deviceSn\":\"DSSV20E050638917C00005\"}";
//        System.out.println(SignUtils.getContentMD5(content3.getBytes()));
//    }

//    @Test
//    public void test002() throws Exception{
//        Map<String, String> params = Maps.newConcurrentMap();
////        SignUtils.addParams("x-sw-app-id", appId, params);
////        SignUtils.addParams("x-sw-timestamp", "1696990741976", params);
////        SignUtils.addParams("x-sw-req-path", "/live/resource/v2/videos", params);
////        SignUtils.addParams("x-sw-sign-type", "md5", params);
////        SignUtils.addParams("x-sw-sign-headers", "", params);
////        SignUtils.addParams("x-sw-content-md5", "13DE9F14F84BBC8263F9A6AA51D381FB", params);
////        SignUtils.addParams("x-sw-version", "2", params);
//
//        SignUtils.addParams("x-sw-app-id", appId, params);
//        SignUtils.addParams("x-sw-timestamp", "1697011818137", params);
//        SignUtils.addParams("x-sw-req-path", "/live/resource/v2/videos", params);
//        SignUtils.addParams("x-sw-sign-type", "md5", params);
//        SignUtils.addParams("x-sw-sign-headers", "", params);
//        SignUtils.addParams("x-sw-content-md5", "D8D88996BBFFDDD8957FB3D19FF4EE56", params);
//        SignUtils.addParams("x-sw-version", "2", params);
//
//        System.out.println(SignUtils.signRequest(params, secret, signMethod));
//    }

    @Test
    public void test003(){
        System.out.println(System.currentTimeMillis());
    }

    @Test
    public void test004(){
        Calendar calendar = Calendar.getInstance();
        System.out.println(calendar.getTimeInMillis());
        calendar.add(Calendar.MONTH, -2);
        System.out.println(calendar.getTimeInMillis());
    }

    @Test
    public void test005(){
        long time = 1697005366000L;
        System.out.println(new Date(time));

        time = 1691734966000L;
        System.out.println(new Date(time));
    }
}
