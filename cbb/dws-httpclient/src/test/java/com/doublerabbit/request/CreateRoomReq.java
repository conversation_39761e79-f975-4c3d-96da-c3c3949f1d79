package com.doublerabbit.request;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@Builder
public class CreateRoomReq{

    /**
     * 非必须，创建聊天室的账号，如果没有就是无创建者
     */
    private String uid;

    /**
     * 非必须，创建聊天室的平台
     */
    private Integer platform;

    /**
     * 必须，聊天室的名称，长度限制128
     */
    private String name;

    /**
     * 非必须，聊天室密码，空或不填代表无密码即可加入
     */
    private String password;

    /**
     * 非必须，聊天室备注
     */
    private String remark;

    /**
     * 非必须，聊天室成员最大值，无限制
     */
    private Integer max_size;

    /**
     * 非必须，进出房间通知，默认为0，0:不通知其他成员；1 加入房间和退出房间通知；2：仅退出房间时通知
     */
    private Integer is_notify;

    /**
     * 非必须，可加入该聊天室的appid列表，空表示只能本应用内成员加入，不跨应用聊天忽略此项
     */
    private List<String> white_appids;

    /**
     * 非必须，房间有效时间，单位秒，0为长期房间,有效期1年，不填则默认一个月
     */
    @Builder.Default
    private Integer room_period = 0;

    /**
     * 非必须，最多缓存消息数。不填默认0，即不缓存
     */
    @Builder.Default
    private Integer cahce_size = 1000000;

    /**
     * 非必须，直播地址 最大限制256
     */
    private String live_addr;

    /**
     * 非必须，公告 最大限制1k
     */
    private String notice;

    /**
     * 非必须，扩展信息   用于业务自定义存储  最大限制1k
     */
    private String ext;

    /**
     * 非必须，默认为0，只能由创建者修改ext字段；设置为1，可以任意成员设置ext字段
     */
    private Integer ext_editable;

    private String traceid;

    public String getTraceid(){
        return UUID.randomUUID().toString();
    }
}
