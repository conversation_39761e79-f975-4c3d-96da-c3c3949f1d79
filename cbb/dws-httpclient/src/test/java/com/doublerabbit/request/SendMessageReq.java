package com.doublerabbit.request;

import lombok.Builder;
import lombok.Data;

import java.util.UUID;

@Data
@Builder
public class SendMessageReq<T> {

    private String trace_id;

    @Builder.Default
    private Integer target_type = 3;
    private String target_id;

    private Integer target_platform;

    private String target_appid;

    private Integer msg_expire;

    @Builder.Default
    private Integer msg_type = 7;

    private Object msg_notification;

    private T msg_body;

    private String from_id;

    private String from_appid;

    private Integer from_platform;

    private String from_name;

    public String getTrace_id(){
        return UUID.randomUUID().toString();
    }
}
