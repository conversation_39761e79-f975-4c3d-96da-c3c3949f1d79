package com.doublerabbit;

import com.doublerabbit.httpclient.util.Jsons;
import com.doublerabbit.model.EventDto;
import com.doublerabbit.request.CreateRoomReq;
import com.doublerabbit.request.SendMessageReq;
import com.doublerabbit.request.UserRegisterReq;
import com.doublerabbit.response.CreateRoomResp;
import com.doublerabbit.response.SendMessageResp;
import com.doublerabbit.response.UserRegisterResp;
import com.google.common.collect.Maps;
import okhttp3.*;
import org.junit.Test;

import java.io.IOException;
import java.util.Map;

public class IMTest {

    private OkHttpClient httpClient = new OkHttpClient();

    @Test
    public void testRegisterUser(){
        UserRegisterResp res = regUser();
        System.out.println(res);
    }

    /**
     * roomid=1692437323445
     */
    @Test
    public void testCreateRoom(){
        CreateRoomReq roomReq = CreateRoomReq.builder().name("testRoom").build();
        String url = "http://172.20.128.219:32195/im/api/v1/room";
        Request request = new Request.Builder()
                .url(url)
                .headers(getCommonHeaders())
                .post(RequestBody.create(MediaType.parse("applicaton/json"), Jsons.toJson(roomReq)))
                .build();
        try {
            System.out.println(Jsons.toJson(roomReq));
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()){
                CreateRoomResp resp = Jsons.fromJson(response.body().string(), CreateRoomResp.class);
                System.out.println(resp);
            }else {
                System.out.println("http code:" + response.code() + "," + response.message());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testSendMsg(){
        String url = "http://172.20.128.219:32195/im/api/v1/messages";
        EventDto event = EventDto.builder().eventType("te2324890st").eventDesc("test helcfggththththlo").build();
        SendMessageReq<EventDto> messageReq = SendMessageReq.<EventDto>builder()
                .msg_body(event)
                .from_id("12345678")
                .from_appid("25244e065e48c6bb6bad6aa2")
                .target_id("1692437323445")
                .build();
        Request request = new Request.Builder()
                .url(url)
                .headers(getCommonHeaders())
                .post(RequestBody.create(MediaType.parse("applicaton/json"), Jsons.toJson(messageReq)))
                .build();
        try {
            System.out.println(Jsons.toJson(messageReq));
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()){
                SendMessageResp resp = Jsons.fromJson(response.body().string(), SendMessageResp.class);
                System.out.println(resp);
            }else {
                System.out.println("http code:" + response.code() + "," + response.message());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 注册用户
     * @return
     */
    private UserRegisterResp regUser(){
        String url = "http://172.20.128.219:32194/auth/api/v1/verify/user";
        UserRegisterReq req = UserRegisterReq.builder().userId("chenjing02").build();
        Request request = new Request.Builder()
                .url(url)
                .headers(getCommonHeaders())
                .post(RequestBody.create(MediaType.parse("applicaton/json"), Jsons.toJson(req)))
                .build();
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()){
                UserRegisterResp resp = Jsons.fromJson(response.body().string(), UserRegisterResp.class);
                return resp;
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    private Headers getCommonHeaders(){
        String appId = "25244e065e48c6bb6bad6aa2";
        String signature = "d7165b504fb1fcd49c9e08810621ec5f";
        String timestamp = "1";
        Map<String, String> headersMap = Maps.newHashMap();
        headersMap.put("appid", appId);
        headersMap.put("signature", signature);
        headersMap.put("timestamp", timestamp);
        return Headers.of(headersMap);
    }

    private String getSignature(){
        String appId = "25244e065e48c6bb6bad6aa2";
        String timestamp = "1";
        String appSecret = "";
        return "";
    }

}
