package com.doublerabbit;

import cn.hutool.core.io.FileUtil;
import com.doublerabbit.httpclient.DwsAuthHeaderProcessor;
import com.doublerabbit.httpclient.DwsHttpClient;
import com.doublerabbit.httpclient.HeaderProcessor;
import com.doublerabbit.httpclient.RestConnector;
import junit.framework.TestCase;
import junit.framework.TestSuite;
import org.junit.Test;

import java.io.File;

/**
 * Unit test for simple App.
 */
public class AppTest {

    @Test
    public void test001(){
        HeaderProcessor processor = new DwsAuthHeaderProcessor("72f895bbc284467db6e9ed7a87ae7fcf");
        String url = "https://dws.seewo.com/dws/3dmodel/catalogs";
        RestConnector restConnector = new RestConnector(new DwsHttpClient(), processor);
        RestResponse res = restConnector.get(url, RestResponse.class);
        System.out.println(res);
    }

    @Test
    public void test002(){
        String name = FileUtil.getPrefix("abc.mov");
        System.out.println(name);
    }

}
