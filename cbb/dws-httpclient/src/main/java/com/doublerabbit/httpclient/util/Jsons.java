/*
 * Copyright (c) 2018. Beijing QuarkIoe Technology Co.,Ltd.
 * All rights reserved.
 */
package com.doublerabbit.httpclient.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonTokenId;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer;
import com.fasterxml.jackson.databind.module.SimpleModule;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
public class Jsons {

    private static final ObjectMapper mapper    = new ObjectMapper();
    private static final SimpleDateFormat FORMAT    = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

    // static block
    static {

        SimpleModule module = new SimpleModule("DateConverter");
        module.addDeserializer(Object.class, new CustomObjectDeserializer());
        mapper.registerModule(module);
        // 为null不进行序列化
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // 使用单引号
        mapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        // 允许出现特殊字符和转义符
        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        mapper.configure(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);
        //:~ 序列化设置 BEGIN
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false); // 当BEAN为空时不序列化
        //:~ 序列化设置 END

        //:~ 反序列化设置 BEGIN
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false); // 设置输入时忽略在JSON字符串中存在但Java对象实际没有的属性

        mapper.configure(DeserializationFeature.FAIL_ON_NUMBERS_FOR_ENUMS, true); // 禁止使用int代表Enum的order()來反序列化Enum,非常危險

        // mapper.configure(DeserializationConfig.Feature.READ_ENUMS_USING_TO_STRING, true); // 使用enum的toString进行反序列化

        mapper.configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, false); // 设置使用高精度模式，会影响性能

        mapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true); // 强制反序列化数组
        //:~ 反序列化设置 END
    }

    public static ObjectMapper getMapper() {
        return mapper;
    }

    /**
     * 将Map接口类型序列化为Json字符串
     *
     * @param object Map接口类型
     * @return 返回Json字符串
     */
    public static String toJson(Object object) {
        return toJson(object, false);
    }

    /**
     * 针对超大型对像，将Map接口类型序列化为Json字符串, JSON Streaming 实现
     * @param out 输出流
     * @param object Map接口类型
     * @throws IOException
     */
    public static void toJson(OutputStream out, Object object) throws IOException {
        mapper.writeValue(out, object);
    }

    /**
     * 将Map接口类型序列化为Json字符串
     *
     * @param object Map接口类型
     * @param format 格式化
     * @return 返回Json字符串
     */
    public static String toJson(Object object, boolean format) {
        String actual = "";
        try {
            // 采用StringWriter方式可以大大提高转换的性能
            if (format) mapper.enable(SerializationFeature.INDENT_OUTPUT);
            actual = mapper.writeValueAsString(object);
            if (format) mapper.disable(SerializationFeature.INDENT_OUTPUT);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return actual;
    }


    /**
     * 将Map接口类型序列化为Json字节数据
     *
     * @param object Map接口类型
     * @return 返回Json字节数据
     */
    public static byte[] toByte(Object object) {
        byte[] actual=new byte[]{};
        try {
            actual = mapper.writeValueAsBytes(object);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return actual;
    }


    /**
     * 将Json字符串反序列化为Map接口类型
     *
     * @param jsonString Json字符串
     * @return 返回Map接口类型
     */
    public static Map toMap(String jsonString) {
        Map map = null;
        try {
            map = mapper.readValue(jsonString, LinkedHashMap.class);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return map;
    }

    /**
     * 将Json字符串反序列化为Map接口类型
     *
     * @param object Json字符串 或 POJO
     * @return 返回Map接口类型
     */
    public static Map toMap(Object object) {
        Map map = null;
        try {
            String sourceJson = object instanceof String ? String.valueOf(object) : toJson(object);
            map = mapper.readValue(sourceJson, LinkedHashMap.class);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return map;
    }

    /**
     * 将JSON反序列化为实体或实体集合
     *
     * @param jsonString Json字符串
     * @param typeRef    目标类型
     * @param <T>        目标类型
     * @return 类型为T的实体或实体集合
     */
    public static <T> T fromJson(String jsonString, TypeReference typeRef) {
        Object actual = null;
        try {
            actual = mapper.readValue(jsonString, typeRef);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (actual == null) {
            return null;
        }
        return (T) actual;
    }

    /**
     * 将JSON反序列化为实体集合
     *
     * @param json            必须为Json格式对象，可以为String,List,Map
     * @param collectionClazz 集合类型
     * @param elementClazz    集合项类型
     * @param <T>             目标类型
     * @return 类型为T的实体或实体集合
     */
    public static <T> T fromJson(Object json, Class<?> collectionClazz, Class<?>... elementClazz) {
        T actual = null;
        try {
            String sourceJson = json instanceof String ? String.valueOf(json) : toJson(json);
            JavaType javaType = mapper.getTypeFactory().constructParametricType(collectionClazz, elementClazz);
            if (!sourceJson.isEmpty()) actual = mapper.readValue(sourceJson, javaType);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return actual;
    }

    /**
     * 处理java.lang.*对像反序化
     *
     * @param json  必须为Json格式对象，可以为String,List,Map
     * @param clazz 类型
     * @param <T>   类型
     * @return 目标类型
     */
    public static <T> T readValue(Object json, Class<T> clazz) {
        T actual = null;
        try {
            String sourceJson = json instanceof String ? String.valueOf(json) : toJson(json);
            if (!sourceJson.isEmpty()) actual = mapper.readValue(sourceJson, clazz);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return actual;
    }

    /**
     * 将JSON转换为Jackson的ObjectNode
     *
     * @param jsonString Json字符串
     * @return ObjectNode
     */
    public static JsonNode toJsonNode(String jsonString) {
        JsonNode actual = null;
        try {
            actual = mapper.readValue(jsonString, JsonNode.class);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return actual;
    }

    /**
     * 将Map转换为Jackson的ObjectNode
     *
     * @param map 被转换的对象
     * @return ObjectNode
     */
    public static JsonNode toJsonNode(Map map) {
        JsonNode actual = toJsonNode(toJson(map));
        return actual;
    }

    /**
     * 将源实体信息覆盖到目标实体中，当源实体只包含目标实体部分属性时，只会覆盖该部分属性
     *
     * @param source 源实体
     * @param target 目标实体
     * @param <T>    目标实体类型
     * @return 更新后的目标实体
     */
    public static <T> T update(Object source, T target) {
        try {
            String sourceJson = source instanceof String ? String.valueOf(source) : toJson(source);
            if (!sourceJson.isEmpty()) return mapper.readerForUpdating(target).readValue(sourceJson);
        } catch (JsonProcessingException e) {
            log.warn("update object:{} to object:{} error.", source, target, e);
        } catch (IOException e) {
            log.warn("update object:" + source + " to object:" + target + " error.", e);
        }
        return target;
    }

    public static TypeReference getTypeReference(Type type) {
        return ClassTypeReference.getInstance(type);
    }

    static class CustomObjectDeserializer extends UntypedObjectDeserializer {

        public CustomObjectDeserializer() {
            super(null, null);
        }

        @Override
        public Object deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            if (p.getCurrentTokenId() == JsonTokenId.ID_STRING) {
                try {
                    String value = p.getText();
                    // put your own parser here
                    return FORMAT.parse(value);
                } catch (Exception e) {
                    return super.deserialize(p, ctxt);
                }
            } else {
                return super.deserialize(p, ctxt);
            }
        }
    }

    public static class ClassTypeReference extends TypeReference<Object> {
        private final Type type;

        private ClassTypeReference(Type type) {
            this.type = type;
        }

        @Override
        public Type getType() {
            return this.type;
        }

        public static ClassTypeReference getInstance(Type type) {
            return new ClassTypeReference(type);
        }
    }

}
