package com.doublerabbit.httpclient;

public class DwsMediaType {

    public static final java.lang.String APPLICATION_XML = "application/xml";
    public static final java.lang.String APPLICATION_ATOM_XML = "application/atom+xml";
    public static final java.lang.String APPLICATION_XHTML_XML = "application/xhtml+xml";
    public static final java.lang.String APPLICATION_SVG_XML = "application/svg+xml";
    public static final java.lang.String APPLICATION_JSON = "application/json";
    public static final java.lang.String APPLICATION_FORM_URLENCODED = "application/x-www-form-urlencoded";
    public static final java.lang.String MULTIPART_FORM_DATA = "multipart/form-data";
    public static final java.lang.String APPLICATION_OCTET_STREAM = "application/octet-stream";
    public static final java.lang.String TEXT_PLAIN = "text/plain";
    public static final java.lang.String TEXT_XML = "text/xml";
    public static final java.lang.String TEXT_HTML = "text/html";

}
