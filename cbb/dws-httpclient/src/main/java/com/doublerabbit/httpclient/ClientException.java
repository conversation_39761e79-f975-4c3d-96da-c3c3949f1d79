/*
 * Copyright 2019 Guangdong Etone Technology Co.,Ltd.
 * All rights reserved.
 */
package com.doublerabbit.httpclient;

public class ClientException extends RuntimeException {

    private int  httpStatusCode   = -1;

    public ClientException(String string) {
        super(string);
    }

    public ClientException(String string, Throwable t) {
        super(string, t);
    }

    public ClientException(int httpStatusCode, String string) {
        super(string);
        this.httpStatusCode = httpStatusCode;
    }

    public int getHttpStatus() {
        return this.httpStatusCode;
    }
}
