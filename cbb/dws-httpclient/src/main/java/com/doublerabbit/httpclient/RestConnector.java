package com.doublerabbit.httpclient;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;

import java.io.IOException;

@Slf4j
public class RestConnector implements RestOperations{

    private static final int READ_TIMEOUT_IN_MILLIS = 180000;

    private final DwsHttpClient client;

    private final ResponseParser responseParser;

    private final HeaderProcessor headerProcessor;

    public RestConnector(DwsHttpClient client, HeaderProcessor headerProcessor){
        this.client = client;
        this.responseParser = new ResponseParser();
        this.headerProcessor = headerProcessor;
    }

    @Override
    public <T> T get(String path, Class<T> responseType) throws ClientException{
        HttpResult httpResult = getResponse(path, DwsMediaType.APPLICATION_JSON);
        if (!httpResult.isSuccess()){
            throw new ClientException(httpResult.getException().getMessage());
        }
        return responseParser.parse(httpResult.getResponse(), responseType);
    }

    private HttpResult getResponse(String path, String mediaType) {
        HttpResult httpResult = new HttpResult();
        Request.Builder request = new Request.Builder()
                .url(path)
                .addHeader("Accept", mediaType);
        headerProcessor.getHeaders().entrySet().parallelStream()
                .forEach(r -> request.addHeader(r.getKey(), r.getValue()));
        try {
            httpResult.setResponse(this.client.newCall(request.build()).execute());
        }catch (IOException e){
            e.printStackTrace();
            httpResult.setException(e);
        }
        return httpResult;
    }
}
