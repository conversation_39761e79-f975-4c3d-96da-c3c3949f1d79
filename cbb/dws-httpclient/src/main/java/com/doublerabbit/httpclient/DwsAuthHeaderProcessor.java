package com.doublerabbit.httpclient;

import com.google.common.collect.Maps;

import java.util.Map;

public class DwsAuthHeaderProcessor implements HeaderProcessor{

    private final String token;

    public DwsAuthHeaderProcessor(String token){
        this.token = token;
    }

    @Override
    public Map<String, String> getHeaders() {
        Map<String, String> authMap = Maps.newConcurrentMap();
        authMap.put("token", this.token);
        return authMap;
    }
}
