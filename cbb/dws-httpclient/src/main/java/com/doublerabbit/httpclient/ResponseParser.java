package com.doublerabbit.httpclient;

import com.doublerabbit.httpclient.util.Jsons;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ResponseParser {

    public ResponseParser(){}

    public <T> T parse(Response response, Class<T> type) throws ClientException {
        this.checkStatus(response);
        return this.read(response, type);
    }

    /**
     * 栓查返回状态，两层，一层是HTTP状态码，一层是业务状态码
     * @param response
     * @throws ClientException
     */
    public void checkStatus(Response response) throws ClientException {
        int status = response.code();

        if (response.isSuccessful()){
            return;
        }
        throw new ClientException(status, this.getErrorMessage(response, status));
    }

    protected String getErrorMessage(Response response, int status) {
        String errorMessage = "Http status code: " + status;
        if (StringUtils.isNotBlank(response.message())) {
            errorMessage = errorMessage + "\n" + response.message();
        }
        return errorMessage;
    }

    private <T> T read(Response response, Class<T> clazz) {
        try {
            String respStr = response.body().string();
            return Jsons.fromJson(respStr, clazz);
        }catch (Exception e){
            log.error("read error", e);
        }
        throw new ClientException("read data exception");
    }

}
