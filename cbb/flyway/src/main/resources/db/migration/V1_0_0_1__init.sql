CREATE TABLE project (
                         id BIGINT NOT NULL COMMENT '主键',
                         name VA<PERSON><PERSON>R(32) NOT NULL COMMENT '项目名称',
                         cid VARCHAR(64) NOT NULL COMMENT '课件ID',
                         group_id VARCHAR(32) NOT NULL COMMENT '项目归属组织ID',
                         ext VARCHAR(255) NULL COMMENT '扩展字段项',
                         thumbnail VARCHAR(255) NULL COMMENT '缩略图',
                         description VARCHAR(1024) NULL COMMENT '项目描述',
                         is_delete tinyint DEFAULT 0 COMMENT '是否删除',

                         version INT NOT NULL DEFAULT 1 COMMENT '版本',
                         created_at BIGINT(20) UNSIGNED NOT NULL COMMENT '创建时间',
                         created_by VARCHAR(32) NULL COMMENT '创建用户',
                         updated_at BIGINT(20) UNSIGNED NOT NULL COMMENT '最后更新时间',
                         updated_by VARCHAR(32) NULL COMMENT '最后更新用户',
                         PRIMARY KEY (id)
) COMMENT='可视化看板项目表';