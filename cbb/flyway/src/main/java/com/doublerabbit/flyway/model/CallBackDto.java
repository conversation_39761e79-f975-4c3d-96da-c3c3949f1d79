package com.doublerabbit.flyway.model;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
@Data
public class CallBackDto {

    private Long timestamp;
    private int type;

    private String taskId;
    private String target;
    private String streamId;
    private int mode;
    private String extras;
    private int code;
    private String message;

    private Map<String,Object> config;
    private Map<String,Object> output;

}
