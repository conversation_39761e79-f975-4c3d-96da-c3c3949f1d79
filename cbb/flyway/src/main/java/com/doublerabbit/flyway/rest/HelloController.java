package com.doublerabbit.flyway.rest;

import com.alibaba.fastjson2.JSON;
import com.doublerabbit.flyway.model.CallBackDto;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
@RestController
public class HelloController {

    @GetMapping("/hello")
    public String hello() {
        return "Hello world!";
    }

    @PostMapping("/device/gbs/record/callback")
    public ResponseEntity callback(@RequestBody CallBackDto callBackDto){
        if (Objects.isNull(callBackDto)){
            System.out.println("callback is null");
        }else {
            System.out.println(JSON.toJSON(callBackDto));
        }
        return ResponseEntity.noContent().build();
    }

}
