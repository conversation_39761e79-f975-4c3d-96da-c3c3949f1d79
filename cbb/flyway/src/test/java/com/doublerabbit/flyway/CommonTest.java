package com.doublerabbit.flyway;

import ch.qos.logback.core.net.SyslogOutputStream;
import lombok.ToString;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import java.io.File;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.BitSet;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
public class CommonTest {

    @Test
    public void test001(){
        System.out.println(System.currentTimeMillis());
    }

    @Test
    public void test002(){
        Long startTime = 1675823770000L;
        Long endTime = 1675823770068L;
        System.out.println(endTime - startTime);
    }

    @Test
    public void test003(){
        Date date = new Date(1676338066000L);
        System.out.println(date);
    }

    @Test
    public void test004(){
        Date date = new Date();
        System.out.println(date.getTime() + "," + (date.getTime() - 3600*100*1000L));
    }

    @Test
    public void test005(){
        String sn = "48ujmjyyhnjdfeg7uk";
        System.out.println(hashCode(sn));
    }

    @Test
    public void test006(){
        String path = "\\abc\\123";
        String replace = path.replaceAll("\\\\", File.separator);
        System.out.println(replace);
    }

    public Integer hashCode(String ... args) {
        final int prime = 31;
        Integer result = 1;
        for (String r : args){
            result = prime * result + ((r == null) ? 0 : r.hashCode());
        }
        return Math.abs(result);
    }

    @Test
    public void tetst007(){
        Long time = 1682480323487L;
        System.out.println(new Date(time));
        time = 1683771406835L;
        System.out.println(new Date(time));
    }

    @Test
    public void test009(){
        File file = new File("/Users/<USER>/Downloads/7b1155be03a34f088bb88386e27f44e5.mp4");
        if (file.exists()){
            BigInteger size = FileUtils.sizeOfAsBigInteger(file);
            System.out.println(size);
            System.out.println(size.compareTo(BigInteger.valueOf(10 * 1024 * 1024L)));
        }
    }
}
