package com.doublerabbit.rocketmq.consumer;

//import com.cisp.gateway.sdk.annotation.RocketMQMessageListener;
//import com.cisp.gateway.sdk.listener.RocketMQListener;
//import com.cisp.gateway.sdk.processor.AbstractMessageProcessor;
//import com.cisp.gateway.sdk.processor.JsonMessageProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.*;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.SmartLifecycle;
import org.springframework.util.Assert;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * RocketMQ 监听器容器
 * 负责管理所有的 RocketMQ 消费者实例
 */
@Slf4j
public class RocketMQListenerContainer implements InitializingBean, DisposableBean, SmartLifecycle, ApplicationContextAware {
    
    /**
     * RocketMQ 名称服务器地址
     */
    private String nameServerAddress;
    
    /**
     * 消费者实例映射表
     */
    private final Map<String, DefaultMQPushConsumer> consumerMap = new ConcurrentHashMap<>();
    
    /**
     * 容器是否正在运行
     */
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    /**
     * Spring 应用上下文
     */
    private ApplicationContext applicationContext;
    
    public RocketMQListenerContainer(String nameServerAddress) {
        this.nameServerAddress = nameServerAddress;
    }
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
    
    @Override
    public void afterPropertiesSet() {
        // 初始化时不做任何操作，等待 start() 方法调用
    }
    
    @Override
    public void start() {
        if (running.compareAndSet(false, true)) {
            initConsumers();
            startConsumers();
        }
    }
    
    @Override
    public void stop() {
        if (running.compareAndSet(true, false)) {
            stopConsumers();
        }
    }
    
    @Override
    public boolean isRunning() {
        return running.get();
    }
    
    @Override
    public int getPhase() {
        // 设置为最高优先级，确保在其他组件之前启动
        return Integer.MAX_VALUE;
    }
    
    @Override
    public boolean isAutoStartup() {
        return true;
    }
    
    @Override
    public void stop(Runnable callback) {
        stop();
        callback.run();
    }
    
    @Override
    public void destroy() {
        stopConsumers();
    }
    
    /**
     * 初始化所有消费者
     */
    private void initConsumers() {
//        Map<String, Object> beans = applicationContext.getBeansWithAnnotation(RocketMQMessageListener.class);
//        if (beans.isEmpty()) {
//            log.warn("No RocketMQMessageListener found in Spring context");
//            return;
//        }
//
//        for (Object bean : beans.values()) {
//            if (bean instanceof RocketMQListener) {
//                createConsumer((RocketMQListener<?>) bean);
//            } else {
//                log.warn("Bean with RocketMQMessageListener annotation is not instance of RocketMQListener: {}",
//                        bean.getClass().getName());
//            }
//        }
    }
    
    /**
     * 创建消费者实例
     * @param listener 消息监听器
     */
//    @SuppressWarnings({"unchecked", "rawtypes"})
//    private void createConsumer(RocketMQListener<?> listener) {
//        Class<?> listenerClass = listener.getClass();
//        RocketMQMessageListener annotation = listenerClass.getAnnotation(RocketMQMessageListener.class);
//        Assert.notNull(annotation, "RocketMQMessageListener annotation not found on class " + listenerClass.getName());
//
//        String consumerGroup = annotation.consumerGroup();
//        String topic = annotation.topic();
//        String tag = annotation.tag();
//
//        // 获取消息体类型
//        Class<?> messageType = String.class;
//        Type[] interfaces = listenerClass.getGenericInterfaces();
//        for (Type type : interfaces) {
//            if (type instanceof ParameterizedType) {
//                ParameterizedType parameterizedType = (ParameterizedType) type;
//                if (parameterizedType.getRawType().equals(RocketMQListener.class)) {
//                    Type actualTypeArgument = parameterizedType.getActualTypeArguments()[0];
//                    if (actualTypeArgument instanceof Class) {
//                        messageType = (Class<?>) actualTypeArgument;
//                        break;
//                    }
//                }
//            }
//        }
//
//        // 创建消息处理器
//        AbstractMessageProcessor processor = new JsonMessageProcessor(listener, messageType);
//
//        // 创建消费者
//        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(consumerGroup);
//        consumer.setNamesrvAddr(nameServerAddress);
//        consumer.setConsumeThreadMax(annotation.consumeThreadMax());
//
//        // 设置消息模型
//        if (annotation.messageModel() == RocketMQMessageListener.MessageModel.BROADCASTING) {
//            consumer.setMessageModel(MessageModel.BROADCASTING);
//        } else {
//            consumer.setMessageModel(MessageModel.CLUSTERING);
//        }
//
//        // 设置最大重试次数
//        if (annotation.maxReconsumeTimes() > 0) {
//            consumer.setMaxReconsumeTimes(annotation.maxReconsumeTimes());
//        }
//
//        // 设置实例名称
//        if (!annotation.instanceName().isEmpty()) {
//            consumer.setInstanceName(annotation.instanceName());
//        }
//
//        try {
//            // 订阅主题和标签
//            consumer.subscribe(topic, tag);
//
//            // 注册消息监听器
//            if (annotation.consumeMode() == RocketMQMessageListener.ConsumeMode.ORDERLY) {
//                consumer.registerMessageListener(new MessageListenerOrderly() {
//                    @Override
//                    public ConsumeOrderlyStatus consumeMessage(List<MessageExt> msgs, ConsumeOrderlyContext context) {
//                        return processor.processOrderly(msgs, context);
//                    }
//                });
//            } else {
//                consumer.registerMessageListener(new MessageListenerConcurrently() {
//                    @Override
//                    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
//                        return processor.processConcurrently(msgs, context);
//                    }
//                });
//            }
//
//            // 保存消费者实例
//            String consumerKey = consumerGroup + "#" + topic + "#" + tag;
//            consumerMap.put(consumerKey, consumer);
//
//            log.info("Created RocketMQ consumer: group={}, topic={}, tag={}", consumerGroup, topic, tag);
//        } catch (MQClientException e) {
//            log.error("Failed to create RocketMQ consumer: group={}, topic={}, tag={}",
//                    consumerGroup, topic, tag, e);
//        }
//    }
    
    /**
     * 启动所有消费者
     */
    private void startConsumers() {
        for (Map.Entry<String, DefaultMQPushConsumer> entry : consumerMap.entrySet()) {
            String consumerKey = entry.getKey();
            DefaultMQPushConsumer consumer = entry.getValue();
            try {
                consumer.start();
                log.info("Started RocketMQ consumer: {}", consumerKey);
            } catch (MQClientException e) {
                log.error("Failed to start RocketMQ consumer: {}", consumerKey, e);
            }
        }
    }
    
    /**
     * 停止所有消费者
     */
    private void stopConsumers() {
        for (Map.Entry<String, DefaultMQPushConsumer> entry : consumerMap.entrySet()) {
            String consumerKey = entry.getKey();
            DefaultMQPushConsumer consumer = entry.getValue();
            try {
                consumer.shutdown();
                log.info("Shutdown RocketMQ consumer: {}", consumerKey);
            } catch (Exception e) {
                log.error("Failed to shutdown RocketMQ consumer: {}", consumerKey, e);
            }
        }
        consumerMap.clear();
    }
}
