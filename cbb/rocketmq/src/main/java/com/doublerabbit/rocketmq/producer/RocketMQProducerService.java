package com.doublerabbit.rocketmq.producer;

import com.doublerabbit.rocketmq.config.RocketMQConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;

/**
 * RocketMQ producer service
 */
@Slf4j
@Service
public class RocketMQProducerService {
    
    @Autowired
    private RocketMQConfig rocketMQConfig;
    
    private DefaultMQProducer producer;
    
    /**
     * Initialize the RocketMQ producer
     */
    @PostConstruct
    public void init() {
        log.info("Initializing RocketMQ producer...");
        
        producer = new DefaultMQProducer(rocketMQConfig.getProducer().getGroup());
        producer.setNamesrvAddr(rocketMQConfig.getNameServer());
        producer.setSendMsgTimeout(rocketMQConfig.getProducer().getSendMessageTimeout());
        producer.setCompressMsgBodyOverHowmuch(rocketMQConfig.getProducer().getCompressMsgBodyOverHowmuch());
        producer.setMaxMessageSize(rocketMQConfig.getProducer().getMaxMessageSize());
        
        try {
            producer.start();
            log.info("RocketMQ producer started successfully");
        } catch (MQClientException e) {
            log.error("Failed to start RocketMQ producer", e);
            throw new RuntimeException("Failed to start RocketMQ producer", e);
        }
    }
    
    /**
     * Shutdown the RocketMQ producer
     */
    @PreDestroy
    public void shutdown() {
        if (producer != null) {
            producer.shutdown();
            log.info("RocketMQ producer shutdown");
        }
    }
    
    /**
     * Send a message synchronously
     *
     * @param topic Topic to send the message to
     * @param message Message content
     * @return SendResult
     */
    public SendResult sendSync(String topic, String message) throws Exception {
        return sendSync(topic, "", message);
    }
    
    /**
     * Send a message synchronously with a tag
     *
     * @param topic Topic to send the message to
     * @param tag Message tag
     * @param message Message content
     * @return SendResult
     */
    public SendResult sendSync(String topic, String tag, String message) throws Exception {
        Message msg = new Message(topic, tag, message.getBytes(StandardCharsets.UTF_8));
        return producer.send(msg);
    }
    
    /**
     * Send a message asynchronously
     *
     * @param topic Topic to send the message to
     * @param message Message content
     * @param sendCallback Callback to handle the result
     */
    public void sendAsync(String topic, String message, SendCallback sendCallback) throws Exception {
        sendAsync(topic, "", message, sendCallback);
    }
    
    /**
     * Send a message asynchronously with a tag
     *
     * @param topic Topic to send the message to
     * @param tag Message tag
     * @param message Message content
     * @param sendCallback Callback to handle the result
     */
    public void sendAsync(String topic, String tag, String message, SendCallback sendCallback) throws Exception {
        Message msg = new Message(topic, tag, message.getBytes(StandardCharsets.UTF_8));
        producer.send(msg, sendCallback);
    }
    
    /**
     * Send a message in one-way mode (no response)
     *
     * @param topic Topic to send the message to
     * @param message Message content
     */
    public void sendOneWay(String topic, String message) throws Exception {
        sendOneWay(topic, "", message);
    }
    
    /**
     * Send a message in one-way mode (no response) with a tag
     *
     * @param topic Topic to send the message to
     * @param tag Message tag
     * @param message Message content
     */
    public void sendOneWay(String topic, String tag, String message) throws Exception {
        Message msg = new Message(topic, tag, message.getBytes(StandardCharsets.UTF_8));
        producer.sendOneway(msg);
    }
}
