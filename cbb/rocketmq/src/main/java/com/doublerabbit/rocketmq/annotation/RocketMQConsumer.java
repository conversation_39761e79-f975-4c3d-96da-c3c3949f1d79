package com.doublerabbit.rocketmq.annotation;

import java.lang.annotation.*;

/**
 * Custom annotation for RocketMQ consumer methods
 * This annotation can be applied to methods to mark them as RocketMQ message consumers
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RocketMQConsumer {
    
    /**
     * Topic to consume messages from
     */
    String topic();
    
    /**
     * Consumer group name
     * If not specified, will use the default from configuration
     */
    String consumerGroup() default "";
    
    /**
     * Message tag to filter messages
     * Default is "*" which means no filtering
     */
    String tag() default "*";
    
    /**
     * Consumer mode: CLUSTERING or BROADCASTING
     * Default is CLUSTERING
     */
    String messageModel() default "CLUSTERING";
    
    /**
     * Maximum number of messages to consume in a single batch
     */
    int consumeMessageBatchMaxSize() default 1;
}
