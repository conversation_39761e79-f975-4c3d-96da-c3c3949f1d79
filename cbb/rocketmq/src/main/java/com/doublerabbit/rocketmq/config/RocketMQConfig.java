package com.doublerabbit.rocketmq.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * RocketMQ configuration properties
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "rocketmq")
public class RocketMQConfig {
    
    /**
     * RocketMQ name server address
     */
    private String nameServer;
    
    /**
     * Producer configuration
     */
    private ProducerConfig producer;
    
    /**
     * Consumer configuration
     */
    private ConsumerConfig consumer;
    
    @Data
    public static class ProducerConfig {
        /**
         * Producer group name
         */
        private String group;
        
        /**
         * Send message timeout in milliseconds
         */
        private int sendMessageTimeout = 3000;
        
        /**
         * Compress message body threshold, above which the message body will be compressed
         */
        private int compressMsgBodyOverHowmuch = 4096;
        
        /**
         * Maximum number of messages in a batch
         */
        private int maxMessageSize = 4194304;
    }
    
    @Data
    public static class ConsumerConfig {
        /**
         * Consumer group name
         */
        private String group;
        
        /**
         * Consumer thread count
         */
        private int consumeThreadMin = 20;
        
        /**
         * Maximum consumer thread count
         */
        private int consumeThreadMax = 64;
    }
}
