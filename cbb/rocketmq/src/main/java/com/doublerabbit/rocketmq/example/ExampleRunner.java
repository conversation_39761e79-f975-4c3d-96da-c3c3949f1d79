package com.doublerabbit.rocketmq.example;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * Example runner to demonstrate RocketMQ functionality
 */
@Slf4j
@Component
public class ExampleRunner implements CommandLineRunner {
    
    @Autowired
    private ExampleProducer exampleProducer;
    
    @Override
    public void run(String... args) throws Exception {
        log.info("Starting RocketMQ example...");

        // Wait a bit for consumers to start
        Thread.sleep(5000);

        // Send example messages
//        exampleProducer.sendSyncMessage("example-topic", "This is a sync message");
//        exampleProducer.sendAsyncMessage("example-topic", "This is an async message");
//        exampleProducer.sendOneWayMessage("example-topic", "This is a one-way message");

        // Send order messages
        exampleProducer.sendSyncMessage("order-topic", "This is an order message");

        // Send notification messages
//        exampleProducer.sendSyncMessage("notification-topic", "This is a notification message");

        log.info("RocketMQ example completed");
    }
}
