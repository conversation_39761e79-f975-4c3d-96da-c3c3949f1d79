package com.doublerabbit.rocketmq.example;

import com.doublerabbit.rocketmq.producer.RocketMQProducerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Example RocketMQ producer
 */
@Slf4j
@Component
public class ExampleProducer {
    
    @Autowired
    private RocketMQProducerService producerService;
    
    /**
     * Send a message synchronously
     *
     * @param topic Topic to send the message to
     * @param message Message content
     * @return SendResult
     */
    public SendResult sendSyncMessage(String topic, String message) {
        try {
            log.info("Sending sync message to topic: {}, message: {}", topic, message);
            SendResult sendResult = producerService.sendSync(topic, message);
            log.info("Sync message sent successfully, result: {}", sendResult);
            return sendResult;
        } catch (Exception e) {
            log.error("Failed to send sync message", e);
            throw new RuntimeException("Failed to send sync message", e);
        }
    }
    
    /**
     * Send a message asynchronously
     *
     * @param topic Topic to send the message to
     * @param message Message content
     */
    public void sendAsyncMessage(String topic, String message) {
        try {
            log.info("Sending async message to topic: {}, message: {}", topic, message);
            producerService.sendAsync(topic, message, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("Async message sent successfully, result: {}", sendResult);
                }
                
                @Override
                public void onException(Throwable e) {
                    log.error("Failed to send async message", e);
                }
            });
        } catch (Exception e) {
            log.error("Failed to send async message", e);
            throw new RuntimeException("Failed to send async message", e);
        }
    }
    
    /**
     * Send a message in one-way mode (no response)
     *
     * @param topic Topic to send the message to
     * @param message Message content
     */
    public void sendOneWayMessage(String topic, String message) {
        try {
            log.info("Sending one-way message to topic: {}, message: {}", topic, message);
            producerService.sendOneWay(topic, message);
            log.info("One-way message sent");
        } catch (Exception e) {
            log.error("Failed to send one-way message", e);
            throw new RuntimeException("Failed to send one-way message", e);
        }
    }
}
