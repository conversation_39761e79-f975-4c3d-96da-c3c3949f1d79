package com.doublerabbit.rocketmq.consumer;

import com.doublerabbit.rocketmq.annotation.RocketMQConsumer;
import com.doublerabbit.rocketmq.config.RocketMQConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * RocketMQ consumer service
 * Scans for methods annotated with @RocketMQConsumer and registers them as message consumers
 */
@Slf4j
@Service
public class RocketMQConsumerService {
    
    @Autowired
    private RocketMQConfig rocketMQConfig;
    
    @Autowired
    private ApplicationContext applicationContext;
    
    private final List<DefaultMQPushConsumer> consumers = new ArrayList<>();
    
    /**
     * Initialize the RocketMQ consumers
     */
    @PostConstruct
    public void init() {
        log.info("Initializing RocketMQ consumers...");
        
        // Scan for methods annotated with @RocketMQConsumer
        Map<String, Map<Object, Method>> topicConsumers = scanConsumers();
        
        // Create consumers for each topic
        for (Map.Entry<String, Map<Object, Method>> entry : topicConsumers.entrySet()) {
            String topic = entry.getKey();
            Map<Object, Method> handlers = entry.getValue();
            
            try {
                // Create a consumer for this topic
                createConsumer(topic, handlers);
            } catch (Exception e) {
                log.error("Failed to create consumer for topic: " + topic, e);
            }
        }
        
        log.info("RocketMQ consumers initialized successfully");
    }
    
    /**
     * Shutdown all consumers
     */
    @PreDestroy
    public void shutdown() {
        for (DefaultMQPushConsumer consumer : consumers) {
            consumer.shutdown();
        }
        log.info("RocketMQ consumers shutdown");
    }
    
    /**
     * Scan for methods annotated with @RocketMQConsumer
     *
     * @return Map of topics to handlers (object instance and method)
     */
    private Map<String, Map<Object, Method>> scanConsumers() {
        Map<String, Map<Object, Method>> topicConsumers = new HashMap<>();
        
        // Get all beans from the application context
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        for (String beanName : beanNames) {
            Object bean = applicationContext.getBean(beanName);
            Class<?> clazz = bean.getClass();
            
            // Check all methods for the annotation
            for (Method method : clazz.getMethods()) {
                RocketMQConsumer annotation = method.getAnnotation(RocketMQConsumer.class);
                if (annotation != null) {
                    String topic = annotation.topic();
                    
                    // Validate method signature
                    if (method.getParameterCount() != 1 || !method.getParameterTypes()[0].equals(String.class)) {
                        log.warn("Invalid consumer method signature: " + method + ". Method should accept a single String parameter.");
                        continue;
                    }
                    
                    // Add to the map
                    topicConsumers.computeIfAbsent(topic, k -> new HashMap<>()).put(bean, method);
                    log.info("Found RocketMQ consumer method: " + method + " for topic: " + topic);
                }
            }
        }
        
        return topicConsumers;
    }
    
    /**
     * Create a consumer for a topic
     *
     * @param topic Topic to consume from
     * @param handlers Map of handlers (object instance and method)
     */
    private void createConsumer(String topic, Map<Object, Method> handlers) throws MQClientException {
        // Get the first handler to get the annotation details
        Map.Entry<Object, Method> firstEntry = handlers.entrySet().iterator().next();
        Method method = firstEntry.getValue();
        RocketMQConsumer annotation = method.getAnnotation(RocketMQConsumer.class);
        
        // Create the consumer
        String consumerGroup = annotation.consumerGroup().isEmpty() ? 
                rocketMQConfig.getConsumer().getGroup() : annotation.consumerGroup();
        
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(consumerGroup);
        consumer.setNamesrvAddr(rocketMQConfig.getNameServer());
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET);
        consumer.setConsumeThreadMin(rocketMQConfig.getConsumer().getConsumeThreadMin());
        consumer.setConsumeThreadMax(rocketMQConfig.getConsumer().getConsumeThreadMax());
        consumer.setMessageModel(MessageModel.valueOf(annotation.messageModel()));
        consumer.setConsumeMessageBatchMaxSize(annotation.consumeMessageBatchMaxSize());
        
        // Subscribe to the topic
        consumer.subscribe(topic, annotation.tag());
        
        // Register the message listener
        consumer.registerMessageListener(new MessageListenerConcurrently() {
            @Override
            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
                for (MessageExt msg : msgs) {
                    String messageBody = new String(msg.getBody(), StandardCharsets.UTF_8);
                    String messageTag = msg.getTags();
                    
                    log.info("Received message: topic={}, tag={}, msgId={}, body={}", 
                            msg.getTopic(), messageTag, msg.getMsgId(), messageBody);
                    
                    // Invoke all handlers for this topic
                    for (Map.Entry<Object, Method> entry : handlers.entrySet()) {
                        Object bean = entry.getKey();
                        Method method = entry.getValue();
                        
                        try {
                            method.invoke(bean, messageBody);
                        } catch (Exception e) {
                            log.error("Failed to invoke consumer method: " + method, e);
                            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                        }
                    }
                }
                
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        });
        
        // Start the consumer
        consumer.start();
        log.info("RocketMQ consumer started for topic: " + topic);
        
        // Add to the list of consumers
        consumers.add(consumer);
    }
}
