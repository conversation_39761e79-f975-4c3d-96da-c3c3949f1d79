package com.doublerabbit.rocketmq.example;

import com.doublerabbit.rocketmq.annotation.RocketMQConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Example RocketMQ consumer
 */
@Slf4j
@Component
public class ExampleConsumer {
    
    /**
     * Handle messages from the "example-topic"
     * This method will be automatically registered as a consumer by the RocketMQConsumerService
     *
     * @param message Message content
     */
    @RocketMQConsumer(topic = "example-topic")
    public void handleExampleMessage(String message) {
        log.info("Received message from example-topic: {}", message);
        // Process the message
    }
    
    /**
     * Handle messages from the "order-topic" with tag "create"
     * This method will be automatically registered as a consumer by the RocketMQConsumerService
     *
     * @param message Message content
     */
//    @RocketMQConsumer(topic = "order-topic", tag = "create", consumerGroup = "order-consumer-group")
    @RocketMQConsumer(topic = "order-topic", consumerGroup = "order-consumer-group")
    public void handleOrderCreateMessage(String message) {
        log.info("Received order creation message: {}", message);
        // Process the order creation message
    }
    
    /**
     * Handle messages from the "notification-topic" in broadcasting mode
     * This method will be automatically registered as a consumer by the RocketMQConsumerService
     *
     * @param message Message content
     */
    @RocketMQConsumer(
            topic = "notification-topic", 
            messageModel = "BROADCASTING", 
            consumeMessageBatchMaxSize = 10
    )
    public void handleNotificationMessage(String message) {
        log.info("Received notification message: {}", message);
        // Process the notification message
    }
}
