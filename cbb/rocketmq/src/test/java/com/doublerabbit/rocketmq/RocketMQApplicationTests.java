package com.doublerabbit.rocketmq;

import com.doublerabbit.rocketmq.example.ExampleProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.assertNotNull;

@RunWith(SpringRunner.class)
@SpringBootTest
public class RocketMQApplicationTests {
    
    @Autowired
    private ExampleProducer exampleProducer;
    
    @Test
    public void contextLoads() {
        // Verify that the context loads successfully
    }
    
    @Test
    public void testSendSyncMessage() {
        // This test will only work if a RocketMQ server is running
        // Otherwise, it will throw an exception
        try {
            SendResult result = exampleProducer.sendSyncMessage("test-topic", "Test message");
            assertNotNull(result);
        } catch (Exception e) {
            // Ignore exceptions in test environment
            System.out.println("RocketMQ server not available: " + e.getMessage());
        }
    }
}
