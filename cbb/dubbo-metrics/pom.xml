<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>easy-java-parent</artifactId>
        <groupId>com.doublerabbit</groupId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dubbo-metrics</artifactId>
    <packaging>jar</packaging>

    <name>dubbo-metrics</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
<!--        <dependency>-->
<!--            <groupId>com.alibaba.middleware</groupId>-->
<!--            <artifactId>metrics-core-api</artifactId>-->
<!--            <version>2.0.6</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.alibaba.middleware</groupId>
            <artifactId>metrics-threadpool</artifactId>
            <version>${dubbo.metrics.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.middleware</groupId>
            <artifactId>metrics-os</artifactId>
            <version>${dubbo.metrics.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.middleware</groupId>
            <artifactId>metrics-core-impl</artifactId>
            <version>${dubbo.metrics.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.stefanbirkner</groupId>
            <artifactId>system-rules</artifactId>
            <version>1.19.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.dubbo/dubbo-monitor-default -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-monitor-default</artifactId>
            <version>2.7.18</version>
        </dependency>
    </dependencies>
</project>
