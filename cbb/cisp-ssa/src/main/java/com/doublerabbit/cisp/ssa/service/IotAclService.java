package com.doublerabbit.cisp.ssa.service;

import com.seewo.iot.platform.dto.device.DeviceInfoDto;
import com.seewo.iot.platform.dto.device.DeviceListDto;
import com.seewo.iot.platform.dto.group.GroupAddDeviceDto;
import com.seewo.iot.platform.dto.shadow.ShadowDto;
import com.seewo.iot.platform.register.dto.AuthInfoVo;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: IoT 访问控制接口
 */
public interface IotAclService {

    DeviceInfoDto getDeviceById(BigInteger deviceId);

}
