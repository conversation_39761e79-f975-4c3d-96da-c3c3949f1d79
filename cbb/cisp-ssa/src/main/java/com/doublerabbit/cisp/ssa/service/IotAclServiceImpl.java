package com.doublerabbit.cisp.ssa.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.doublerabbit.cisp.ssa.config.SystemConfiguration;
import com.seewo.iot.platform.api.*;
import com.seewo.iot.platform.dto.PageRequestDto;
import com.seewo.iot.platform.dto.device.*;
import com.seewo.iot.platform.dto.device.tag.DeviceTagRequestDto;
import com.seewo.iot.platform.dto.group.GroupAddDeviceDto;
import com.seewo.iot.platform.dto.group.GroupDtoDto;
import com.seewo.iot.platform.dto.shadow.ShadowDto;
import com.seewo.iot.platform.register.api.DeviceRegisterService;
import com.seewo.iot.platform.register.dto.AuthInfoVo;
import com.seewo.iot.platform.register.dto.RegisterVo;
import com.seewo.mis.common.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.adapters.HexBinaryAdapter;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
@Slf4j
@Service
public class IotAclServiceImpl implements IotAclService, CommandLineRunner {

    private static final String SN_IDENTIFIER = "uid";

    @DubboReference
    private DeviceService deviceService;
    @DubboReference
    private GroupApiService groupApiService;
    @DubboReference
    private DeviceShadowService deviceShadowService;

    @DubboReference
    private DeviceTagService tagService;

    @DubboReference
    private DeviceRegisterService registerService;

    @DubboReference
    private DeviceGroupApiService deviceGroupApiService;

    @Autowired
    private SystemConfiguration systemConfiguration;


    @Override
    public DeviceInfoDto getDeviceById(BigInteger deviceId) {
        BaseResponse<DeviceInfoDto> deviceInfo = deviceService.getDeviceInfo(deviceId);
        return deviceInfo.success() ? deviceInfo.getData() : new DeviceInfoDto();
    }

    public void callDevice(){
        BigInteger deviceId = new BigInteger("1149823848013041664");
        Map<String, Object> params = new HashMap<>();
        params.put("deviceId", "12345");
        params.put("deviceStatus", "offline");
        params.put("productKey", "rrgryty");
        params.put("ip", "127.0.0.1");
        BaseResponse baseResponse = deviceService.callDeviceService(new DeviceServiceRequestDto()
                .setDeviceId(deviceId)
                .setServiceKey("commandNotify")
                        .setParams(params)
                .setTraceId(String.valueOf(System.currentTimeMillis())));
        log.info("call device: {}", JSONObject.toJSONString(baseResponse));
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("=========================");
//        BigInteger deviceId = new BigInteger("1142143788613406720");
        BigInteger deviceId = new BigInteger("1149823848013041664");
        DeviceInfoDto deviceInfo = getDeviceById(deviceId);
        log.info("deviceInfo:{}", JSONObject.toJSONString(deviceInfo));
        callDevice();
    }
}
