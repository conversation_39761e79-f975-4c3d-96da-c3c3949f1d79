<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>easy-java-parent</artifactId>
        <groupId>com.doublerabbit</groupId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>spring-akka</artifactId>
    <packaging>jar</packaging>

    <name>spring-akka</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <akka.version>2.10.8</akka.version>
<!--        <akka.version>2.5.22</akka.version>-->
        <scala.binary.version>2.13</scala.binary.version>
<!--        <scala.binary.version>2.12</scala.binary.version>-->
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.doublerabbit</groupId>
                <artifactId>easy-java-bom</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.typesafe.akka</groupId>-->
<!--                <artifactId>akka-bom_${scala.binary.version}</artifactId>-->
<!--                <version>${akka.version}</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.typesafe.akka</groupId>-->
<!--            <artifactId>akka-slf4j_${scala.binary.version}</artifactId>-->
<!--            <version>${akka.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.typesafe.akka</groupId>
            <artifactId>akka-actor_2.13</artifactId>
            <version>2.6.0</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.typesafe.akka</groupId>-->
<!--            <artifactId>akka-actor_${scala.binary.version}</artifactId>-->
<!--            <version>${akka.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

<!--    <repositories>-->
<!--        <repository>-->
<!--            <id>central</id>-->
<!--            <name>Maven Repository Switchboard</name>-->
<!--            <layout>default</layout>-->
<!--            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>-->
<!--            <snapshots>-->
<!--                <enabled>false</enabled>-->
<!--            </snapshots>-->
<!--        </repository>-->

<!--        <repository>-->
<!--            <id>maven21</id>-->
<!--            <name>maven21-releases</name>-->
<!--            <url>http://repo1.maven.org/maven2</url>-->
<!--            <releases>-->
<!--                <updatePolicy>always</updatePolicy>-->
<!--            </releases>-->
<!--            <snapshots>-->
<!--                <updatePolicy>always</updatePolicy>-->
<!--            </snapshots>-->
<!--        </repository>-->
<!--    </repositories>-->

</project>
