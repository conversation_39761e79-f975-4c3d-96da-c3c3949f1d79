package com.doublerabbit.akka.actor;

import akka.actor.UntypedAbstractActor;
import com.doublerabbit.akka.model.Student;
import com.doublerabbit.akka.service.GreetingService;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class TaskActor extends UntypedAbstractActor {

    private GreetingService greetingService;

    public TaskActor(GreetingService greetingService){
        this.greetingService = greetingService;
    }

    @Override
    public void preStart(){
        System.out.println("taskactor prestart======");
    }

    @Override
    public void onReceive(Object message) throws Throwable {
        System.out.println(new Date() + "," +  message);
        if (message instanceof String){
            System.out.println(greetingService.greet(message.toString()));
        }else if (message instanceof Student){
            sender().tell(new Student("测试"), self());
        }
    }
}
