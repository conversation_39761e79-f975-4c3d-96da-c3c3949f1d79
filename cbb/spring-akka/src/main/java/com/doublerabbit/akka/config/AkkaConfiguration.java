package com.doublerabbit.akka.config;

import akka.actor.ActorSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.doublerabbit.akka.config.SpringProvider.SPRING_PROVIDER;

@Configuration
public class AkkaConfiguration {

    @Autowired
    private ApplicationContext context;

    @Bean
    public ActorSystem actorSystem(){
        ActorSystem system = ActorSystem.create("akka-spring");
        SPRING_PROVIDER.get(system).initialize(context);
        return system;
    }

}
