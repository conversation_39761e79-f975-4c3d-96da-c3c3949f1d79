package com.doublerabbit.akka;

import akka.actor.ActorRef;
import akka.actor.ActorSystem;
import akka.actor.Props;
import akka.pattern.Patterns;
import akka.util.Timeout;
import com.doublerabbit.akka.actor.TaskActor;
import com.doublerabbit.akka.model.Student;
import com.doublerabbit.akka.service.GreetingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import scala.concurrent.Await;
import scala.concurrent.Future;
import scala.concurrent.duration.Duration;

import java.util.concurrent.TimeUnit;

/**
 * Hello world!
 * https://www.baeldung.com/akka-with-spring
 * https://www.bookstack.cn/read/guobinhit-akka-guide/ed74746d149bd4c1.md AKKA中文指南
 * https://www.liuhaihua.cn/archives/711343.html
 */
@SpringBootApplication
public class App implements CommandLineRunner
{
    @Autowired
    private ActorSystem actorSystem;

    @Autowired
    private GreetingService greetingService;

    public static void main( String[] args )
    {
        System.out.println( "Hello World!" );
        SpringApplication.run(App.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        ActorRef ref = actorSystem.actorOf(Props.create(TaskActor.class, greetingService), "abcd");
        ref.tell("i am test 001", ActorRef.noSender());
        ref.tell("i am test 002", ActorRef.noSender());
        System.out.println("send all======");

        // 通过路径找到对应的actor
//        actorSystem.actorSelection("/user/abcd").tell("yes", ActorRef.noSender());

        Timeout timeout = new Timeout(Duration.create(10, TimeUnit.SECONDS));
        Future<Object> future = Patterns.ask(ref, new Student("test"), timeout);
        try {
            Object obj = Await.result(future, timeout.duration());
//            String reply = obj.toString();
            System.out.println("reply msg: " + obj);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
