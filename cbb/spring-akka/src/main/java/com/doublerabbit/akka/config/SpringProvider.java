package com.doublerabbit.akka.config;

import akka.actor.AbstractExtensionId;
import akka.actor.ExtendedActorSystem;
import akka.actor.Extension;
import akka.actor.Props;
import org.springframework.context.ApplicationContext;

public class SpringProvider extends AbstractExtensionId<SpringProvider.SpringExt> {

    public static final SpringProvider SPRING_PROVIDER = new SpringProvider();

    @Override
    public SpringExt createExtension(ExtendedActorSystem system) {
        return new SpringExt();
    }

    public static class SpringExt implements Extension{
        private volatile ApplicationContext context;

        public void initialize(ApplicationContext context){
            System.out.println("applicationContext init......");
            this.context = context;
        }

        public Props props(String beanName){
            return Props.create(ActorProducer.class, this.context, beanName);
        }
    }

}
