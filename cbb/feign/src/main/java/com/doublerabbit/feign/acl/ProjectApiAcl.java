package com.doublerabbit.feign.acl;

import feign.Headers;
import feign.Param;
import feign.RequestLine;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 项目接口
 */
public interface ProjectApiAcl {

    @Headers("token: {token}")
    @RequestLine("GET /dws/project/projects")
    ProjectVo getProjects(@Param("token") String token);

    class ProjectVo{

        Integer statusCode;
        String message;
        Object data;

    }

}
