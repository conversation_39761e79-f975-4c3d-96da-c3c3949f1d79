package com.doublerabbit.feign.config;

import com.doublerabbit.feign.acl.ProjectApiAcl;
import feign.Feign;
import feign.gson.GsonDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
@Configuration
public class FeignConfig {

    @Bean
    public ProjectApiAcl projectApiAcl(){
        return Feign.builder()
                .decoder(new GsonDecoder())
                .target(ProjectApiAcl.class, "http://dws.test.seewo.com");
    }

}
