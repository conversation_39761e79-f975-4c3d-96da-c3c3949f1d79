package com.doublerabbit.feign;

import com.doublerabbit.feign.acl.ProjectApiAcl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Hello world!
 *
 */
@SpringBootApplication
public class FeignApp implements CommandLineRunner
{
    @Autowired
    private ProjectApiAcl projectApiAcl;

    public static void main(String[] args) {
        SpringApplication.run(FeignApp.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        String token = "08589b9715e1b47d38e5ce224d930bbb8";
        ProjectApiAcl.ProjectVo projectVo = projectApiAcl.getProjects(token);
        System.out.println(projectVo);
    }
}
