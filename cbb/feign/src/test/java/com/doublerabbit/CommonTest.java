package com.doublerabbit;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
public class CommonTest {

    @Test
    public void test001(){
        System.out.println("=====");
    }

    @Test
    public void test002(){
        String str1 = new String("null");
        String str2 = new String("null");
        System.out.println(str1.equals(str2));
    }

    @Test
    public void test003(){
        String str = "dws-resource/v1-101447639236609-Tile_+003_+002-Tile_+003_+002_L23_0uuuu37520.b3dm";
        str = str.replaceAll("\\+", "_");
        System.out.println(str);
    }

    @Test
    public void test004(){
        String str1 = "1234";
        String str2 = "1234";
        System.out.println(str1.equals(str2));
    }

    @Test
    public void test005(){
        String str = "dws-resource/ v1";
        String regex = "[^\\x00-\\x7F]";
        System.out.println(str.replaceAll(" ", "-"));
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);

        while (matcher.find()) {
            System.out.println("Matched: " + matcher.group());
        }
    }

    @Test
    public void test006(){
        String regex = "[^\\x01-\\x7F]";
        String input = "Hello, 你好！";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        while (matcher.find()) {
            System.out.println("Matched: " + matcher.group());
        }
    }

    private OkHttpClient httpClient = new OkHttpClient();

    /**
     * https://pro-cos-public.seewo.com/seewo-school/f3819709-90ea-03c4-a45f-43ea7f691d13
     * https://pro-cos-public.seewo.com/seewo-school/00d8ebd1-bbd8-3644-fdca-e1483b79673f#2
     * https://cos-public.seewo.com/ dws-record/uwiymypphjmpnhozhjxqmpmwivhhihhh
     */
    @Test
    public void test007() throws Exception{
//        String url = "https://pro-cos-public.seewo.com/seewo-school/00d8ebd1-bbd8-3644-fdca-e1483b79673f#2";
        String url = "https://cos-public.seewo.com/ dws-record/uwiymypphjmpnhozhjxqmpmwivhhihhh";
        Request request = new Request.Builder()
                .url(url)
                .build();
        Response response = httpClient.newCall(request).execute();
        if (response.isSuccessful()){

            InputStream is = response.body().byteStream();
            byte[] buf = new byte[2048];
            int len = 0;
            FileOutputStream fos = null;

            File file = new File("/Users/<USER>/testdoublerabit123");
            fos = new FileOutputStream(file);
            while ((len = is.read(buf)) != -1) {
                fos.write(buf, 0, len);
            }
            fos.flush();
        }
    }

    @Test
    public void test008(){
        String str = "hello";
        String tt = new String("hello");
        System.out.println(str.equals(tt));
    }
}
