package com.doublerabbit;

import com.doublerabbit.okhttp.jsonpath.JsonPathHelper;
import com.doublerabbit.okhttp.jsonpath.Jsons;
import com.fasterxml.jackson.databind.JsonNode;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.internal.JsonContext;
import lombok.Data;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * https://goessner.net/articles/JsonPath/
 * https://www.baeldung.com/guide-to-jayway-jsonpath
 * 参数名变化
 * 参数类型变化，自定义类型的转换
 * 参数层级变化
 *
 * 定义配置
 * 输入-》转换-》输出
 */
public class JsonPathTest {

    @Test
    public void test001(){
        Object dataObject = JsonPath.parse(getJsonStr()).read("$[?(@.id == 2)]");
        String dataString = dataObject.toString();
        System.out.println(dataString);
    }

    @Test
    public void test002(){
        Object dataObject = JsonPath.parse(getJsonStr());
        System.out.println(dataObject);
        Map<String, Object> data = new HashMap<>();
        Object obj = JsonPathHelper.setOrCreateValue(data, "", ((JsonContext) dataObject).json());
        System.out.println(obj);
    }

    @Test
    public void test003(){
        Object dataObject = JsonPath.parse(getJsonStr());
        int length = JsonPath.read(dataObject, "$.length()");
        System.out.println(length);
    }

    @Test
    public void testDiffUpdate(){
        String jsonStr = "{\"appid\":\"1234\"}";
        Config config = new Config();
        config.setLogo("hello");
        JsonNode oldObj = Jsons.toJsonNode(jsonStr);
        Map updateObj = Jsons.toMap(config);
        Map oldMap = Jsons.toMap(jsonStr);
        JsonPathHelper.setOrCreateValues(oldMap, updateObj);
        System.out.println(Jsons.toJson(oldMap));
    }


    @Test
    public void test004(){
        String str = "{\"bizId\": \"resource-development\", \"inputs\": [{\"uid\": \"35682a92745c4f88a276cf116b5f396b\", \"mappings\": [{\"source\": \"$\", \"target\": \"coursewareStat\"}]}, {\"uid\": \"1a0fe08821554f738ebd3ee86453303e\", \"mappings\": [{\"source\": \"teacherCoursewareCnt\", \"target\": \"teacherCoursewareCnt\"}, {\"source\": \"teacherCoursewareCntCompare\", \"target\": \"teacherCoursewareCntCompare\"}, {\"source\": \"teacherTeachingPlanCnt\", \"target\": \"teacherTeachingPlanCnt\"}, {\"source\": \"teacherTeachingPlanCntCompare\", \"target\": \"teacherTeachingPlanCntCompare\"}, {\"source\": \"schoolCoursewareCnt\", \"target\": \"schoolCoursewareCnt\"}, {\"source\": \"schoolCoursewareCntCompare\", \"target\": \"schoolCoursewareCntCompare\"}, {\"source\": \"schoolTeachingPlanCnt\", \"target\": \"schoolTeachingPlanCnt\"}, {\"source\": \"schoolTeachingPlanCntCompare\", \"target\": \"schoolTeachingPlanCntCompare\"}]}]}";
    }

    private String getJsonStr(){
        return "[\n" +
                "    {\n" +
                "        \"id\": 1,\n" +
                "        \"title\": \"Casino Royale\",\n" +
                "        \"director\": \"Martin Campbell\",\n" +
                "        \"starring\": \n" +
                "        [\n" +
                "            \"Daniel Craig\",\n" +
                "            \"Eva Green\"\n" +
                "        ],\n" +
                "        \"desc\": \"Twenty-first James Bond movie\",\n" +
                "        \"release date\": 1163466000000,\n" +
                "        \"box office\": 594275385\n" +
                "    },\n" +
                "\n" +
                "    {\n" +
                "        \"id\": 2,\n" +
                "        \"title\": \"Quantum of Solace\",\n" +
                "        \"director\": \"Marc Forster\",\n" +
                "        \"starring\": \n" +
                "        [\n" +
                "            \"Daniel Craig\",\n" +
                "            \"Olga Kurylenko\"\n" +
                "        ],\n" +
                "        \"desc\": \"Twenty-second James Bond movie\",\n" +
                "        \"release date\": 1225242000000,\n" +
                "        \"box office\": 591692078\n" +
                "    },\n" +
                "\n" +
                "    {\n" +
                "        \"id\": 3,\n" +
                "        \"title\": \"Skyfall\",\n" +
                "        \"director\": \"Sam Mendes\",\n" +
                "        \"starring\": \n" +
                "        [\n" +
                "            \"Daniel Craig\",\n" +
                "            \"Naomie Harris\"\n" +
                "        ],\n" +
                "        \"desc\": \"Twenty-third James Bond movie\",\n" +
                "        \"release date\": 1350954000000,\n" +
                "        \"box office\": 1110526981\n" +
                "    },\n" +
                "\n" +
                "    {\n" +
                "        \"id\": 4,\n" +
                "        \"title\": \"Spectre\",\n" +
                "        \"director\": \"Sam Mendes\",\n" +
                "        \"starring\": \n" +
                "        [\n" +
                "            \"Daniel Craig\",\n" +
                "            \"Lea Seydoux\"\n" +
                "        ],\n" +
                "        \"desc\": \"Twenty-fourth James Bond movie\",\n" +
                "        \"release date\": 1445821200000,\n" +
                "        \"box office\": 879376275\n" +
                "    }\n" +
                "]";
    }

    @Data
    class Config{
        private String appId;
        private String appName;
        private String logo;
    }

}
