package com.doublerabbit;

import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

public class CompleteFutureTest {

    List<CompletableFuture<Boolean>> completableFutureList;

    @Test
    public void test001(){
        List<DownloadTask> tasks = Lists.newArrayList(new CubeDownloadTask(), new ModelDownloadTask());
        completableFutureList = tasks.stream()
                .map(task -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return task.call();
                    }catch (Exception e){
                    }
                    return false;
                }).exceptionally(e -> {
                    handleError(e);
                    throw new CompletionException(e);
                }))
                .collect(Collectors.toList());
        CompletableFuture
                .allOf(completableFutureList.toArray(new CompletableFuture[0]))
                .applyToEither(FutureUtil.timeoutAfter(70*1000L, TimeUnit.MILLISECONDS), Function.identity())
                .exceptionally(e -> {
                    handleError(e);
                    throw new CompletionException(e);
                });
        System.out.println("ready to join ======");
        List<Boolean> resultList = completableFutureList.stream().map(CompletableFuture::join).collect(Collectors.toList());
        resultList.stream().forEach(r -> System.out.println(r));
//        completableFutureList.stream().forEach(r -> r.cancel());
        System.out.println("ready to join end======");
        handleSuccess();
    }

    @Test
    public void test002() throws Exception{
        CompletableFuture
                .allOf(new CompletableFuture[0])
                .applyToEither(FutureUtil.timeoutAfter(30*1000L, TimeUnit.MILLISECONDS), Function.identity())
                .exceptionally(e -> {
                    handleError(e);
                    throw new CompletionException(e);
                });
        Thread.sleep(60*1000L);
        System.out.println("=======");
    }

    private void handleError(Throwable e){
        System.out.println(e.getMessage());
        completableFutureList.stream().forEach(r -> r.cancel(true));

    }

    private void handleSuccess(){
        System.out.println("oh yes success=========");
    }

    public class CubeDownloadTask implements DownloadTask{

        @Override
        public void download() {
            try {
                System.out.println("CubeDownloadTask start =======");
                Thread.sleep(60*1000L);
            }catch (Exception e){}
        }

        @Override
        public Boolean call() throws Exception {
            this.download();
            return true;
        }
    }

    public class ModelDownloadTask implements DownloadTask{

        @Override
        public void download() {
            try {
                System.out.println("ModelDownloadTask =======");
                Thread.sleep(2*60*1000L);
            }catch (Exception e){}
        }

        @Override
        public Boolean call() throws Exception {
            this.download();
            return true;
        }
    }

    public interface DownloadTask extends Callable<Boolean>{
        void download();

    }

}
