package com.doublerabbit;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
public class GuavaCacheTest {

    @Test
    public void test001() throws Exception{
        Cache<String, String> cache = CacheBuilder.newBuilder()
                .expireAfterAccess(500, TimeUnit.MILLISECONDS)
                .maximumSize(1000)
                .removalListener(new CacheRemoval())
                .build();
        cache.put("ab", "123");
        cache.invalidate("ab");
        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
        // 开启定时任务，延迟1秒后执行，每隔5秒执行一次
        executor.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                // 定时任务的逻辑代码
                cache.cleanUp();
            }
        }, 1000, 1000, TimeUnit.MILLISECONDS);
        Thread.sleep(20000L);
//        cache.cleanUp();
//        Thread.sleep(10000L);
    }

    class CacheRemoval implements RemovalListener<String, String> {

        @Override
        public void onRemoval(RemovalNotification<String, String> removalMsg) {
            System.out.println("onRemoval:" + removalMsg.getValue() + "," + removalMsg.getCause() + "," + removalMsg.wasEvicted());
        }
    }
}
