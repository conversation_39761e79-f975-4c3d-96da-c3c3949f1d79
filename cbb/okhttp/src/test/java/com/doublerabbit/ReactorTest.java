package com.doublerabbit;

import com.doublerabbit.okhttp.model.User;
import com.doublerabbit.okhttp.service.ApiService;
import com.doublerabbit.okhttp.service.ApiServiceImpl;
import com.google.common.collect.Lists;
import org.junit.Test;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * https://blog.csdn.net/Prepared/article/details/126510132
 */
public class ReactorTest {

    @Test
    public void test001(){
        Flux.just("hello", "world").subscribe(System.out::println);
    }

    @Test
    public void test002(){
        Long startTime = System.currentTimeMillis();
        List<User> result = Lists.newArrayList();
        List<ApiService> services = Lists.newArrayList(new ApiServiceImpl());
        ApiService apiService = new ApiServiceImpl();
        List<Integer> params = Lists.newArrayList(2000, 3000, 4000);
        Flux<Mono<User>> flux = Flux.fromIterable(params)
                .map(s -> apiService.request(s));
//        flux.subscribeOn(Schedulers.boundedElastic())
//                        .blockLast()
//                        .subscribe(r -> {
////                            User user = r.block();
//                            System.out.println(r);
//                            result.add(r);
//                        });
        flux.subscribe(r -> {
            User user = r.block();
            System.out.println(user);
            result.add(user);
        });
        System.out.println(result);
        System.out.println("cost time:" + (System.currentTimeMillis() - startTime));
    }

    @Test
    public void test003() throws Exception{
        Long startTime = System.currentTimeMillis();
        List<Integer> params = Lists.newArrayList(2000, 3000, 4000);
        List<Mono<User>> res = Lists.newArrayList();
        ApiService apiService = new ApiServiceImpl();
        CountDownLatch latch = new CountDownLatch(1);
        List<User> users = Lists.newArrayList();
        params.stream().forEach(r -> {
            res.add(apiService.request(r));
        });
        Flux.merge(res).subscribe(r -> {
            System.out.println(r+"===,"+ Thread.currentThread().getName());
            users.add(r);
        },e -> {
            System.out.println("get exception " + e.getMessage());
            latch.countDown();
        }, () ->{
            System.out.println(Thread.currentThread().getName());
            System.out.println("do completed cost time:" + (System.currentTimeMillis() - startTime));
//            throw new RuntimeException("oh no");
            latch.countDown();
        });
        latch.await();
        System.out.println(users);
        System.out.println("cost time:" + (System.currentTimeMillis() - startTime));
    }

}
