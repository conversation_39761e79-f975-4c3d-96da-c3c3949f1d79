package com.doublerabbit;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.concurrent.atomic.AtomicInteger;

public class CommonTest {

    @Test
    public void test001(){
        double t1 = 20;
        double t2 = 33;
        double res = t1/(t1+t2)*100;
//        String formattedPercentage = String.format("%.2f%%", res);
        String formattedPercentage = String.format("%.0f", res);
        System.out.println(formattedPercentage);

        double part = 20.0;
        double total = 55;
        double percentage = (part / total) * 100.0;

        System.out.println("百分比：" + percentage + "%");

        BigDecimal b1 = new BigDecimal(20);
        BigDecimal b2 = new BigDecimal(55);
        BigDecimal rig = b1.divide(b2, 0, RoundingMode.FLOOR);
        System.out.println(rig);
    }

    @Test
    public void test002(){
        Pair<AtomicInteger, AtomicInteger> xmPair = Pair.of(new AtomicInteger(20), new AtomicInteger(33));
        Pair<Double, Double> convert = statRatio(xmPair);
        System.out.println(convert.getLeft() + "," + convert.getRight());
    }

    /**
     * 保留两位小数
     * @param obj
     * @return
     */
    public static String formatToNumber(BigDecimal obj) {
        DecimalFormat df = new DecimalFormat("#.00");
        if(obj.compareTo(BigDecimal.ZERO)==0) {
            return "0.00";
        }else if(obj.compareTo(BigDecimal.ZERO)>0&&obj.compareTo(new BigDecimal(1))<0){
            return "0"+df.format(obj).toString();
        }else {
            return df.format(obj).toString();
        }
    }

    private Pair<Double, Double> statRatio(Pair<AtomicInteger, AtomicInteger> data){
        Double left = Double.valueOf(data.getLeft().get());
        Double right = Double.valueOf(data.getRight().get());
        Double resLeft = Double.valueOf(String.format("%.0f", (left/(left + right))*100));
        return Pair.of(resLeft, 100 - resLeft);
    }

}
