package com.doublerabbit;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.List;

public class PositionConvertTest {

    private static List<Position> initData;

    static {
        initData = Lists.newArrayList(
                /*new Position("培元中学", 118.589896,24.923356),
                new Position("泉州实验小学", 118.601719,24.921569),
                new Position("南京交通职业技术学院", 118.918469,31.918547),
                new Position("延安中学附属省吾中学", 121.429519,31.231089),
                new Position("厦门医学院", 118.011341,24.613924),
                new Position("杭州安吉路新天地实验学校", 120.184438,30.325751),
                new Position("桐庐县科技城未来学校", 119.737286,29.815647),
                new Position("华东师范大学附属桐庐学校", 119.681221,29.781859),
                new Position("慧澜中学", 120.172914,30.34083),
                new Position("山东商业职业技术学院", 117.277332,36.639894),
                new Position("宁波镇海中学", 121.726786,29.96109),
                new Position("温州市第八高级中学", 120.772452,27.947283),
                new Position("永嘉县第二职业学校", 120.681389,28.148275),
                new Position("宁波第二技师学院", 121.521193,29.864716),
                new Position("辽宁省实验学校", 123.432619,41.838988),
                new Position("承德医学院", 117.963582,41.039134),
                new Position("中国地质大学", 114.623561,30.463748),
                new Position("华南师范大学（石牌校区）", 113.35785,23.144541),
                new Position("武汉市东西湖职业技术学校", 114.136745,30.621236),
                new Position("玉泉学校（南校区）", 113.534687,23.165405),
                new Position("南京师范大学（随园校区）", 118.773988,32.059149),
                new Position("杭州竺可桢实验学校", 120.15783,30.155281),
                new Position("浙江省传媒学院", 120.347477,30.327739),
                new Position("浙江省三门中学", 121.394694,29.133444),
                new Position("文华学校", 119.155022,36.776889)*/
                new Position("华南师范大学（石牌校区）", 108.886706,34.181355)
        );
    }

    @Test
    public void test001(){
//        double[] res = CoordinateTransformUtil.bd09towgs84(118.589896,24.923356);
//        System.out.println(res);
        initData.parallelStream().forEach(r -> {
            double[] res = CoordinateTransformUtil.bd09towgs84(r.getLng(), r.lat);
            System.out.println(new Position(r.getName(), res[0], res[1]));
        });
    }

    @Test
    public void test002(){
        System.out.println(new Position("培元中学", 118.589896,24.923356));
    }

    @Test
    public void test003(){
        System.out.println(System.currentTimeMillis());
    }

    @Data
    @AllArgsConstructor
    public static class Position{

        private String name;

        private double lng;

        private double lat;

        @Override
        public String toString(){
            return "name:" + name +
                    ",lng:" + new BigDecimal(lng).setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue() +
                    ",lat:" + new BigDecimal(lat).setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
        }

    }

}
