package com.doublerabbit.okhttp.jsonpath.impl;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * @description
 * <AUTHOR> href="mailto:<EMAIL>">lishaoyu</a>
 * @Date created in 2018\8\7 0007
 */
public class ReflectionPropertyAccessorHelper {
    private static final Logger                           logger   = LoggerFactory.getLogger(ReflectionPropertyAccessorHelper.class);
    private static       ReflectionPropertyAccessorHelper instance = new ReflectionPropertyAccessorHelper();
    private              ConcurrentMap<AccessKey, Access> cache    = new ConcurrentHashMap<AccessKey, Access>();

    public static ReflectionPropertyAccessorHelper getInstance() {
        return instance;
    }

    protected ReflectionPropertyAccessorHelper() {
    }

    public boolean setProperty(Object bean, String fieldName, Object fieldValue) {
        CallContext context = new CallContext(bean, fieldName, fieldValue);
        this.executeGetOrSet(context);
        return context.isSuccess();
    }

    public Object getProperty(Object bean, String fieldName) {
        CallContext context = new CallContext(bean, fieldName);
        this.executeGetOrSet(context);
        return context.getValue();
    }

    private void executeGetOrSet(CallContext context) {
        this.getOrSetPropertyInCachedWay(context);
        if (!context.isExecuted()) {
            this.getOrSetPropertyDirect(context);
        }
        if (!context.isExecuted()) {
            this.getOrSetPropertyByGetter(context);
        }
        if (!context.isExecuted()) {
            this.cache.putIfAbsent(context.getAccessKey(), Access.missing());
        }
    }

    void getOrSetPropertyInCachedWay(CallContext context) {
        Access access = this.cache.get(context.getAccessKey());
        if (access == null) {
            return;
        }
        context.markExecuted();
        if (access.hasAnyAccess()) {
            this.invokePropertyOperation(access, context);
        }
    }

    void getOrSetPropertyDirect(CallContext context) {
        Class<?> clazz = context.getBeanClass();
        Field field = this.findField(clazz, context.getFieldName());
        if (field != null) {
            context.markExecuted();
            field.setAccessible(true);
            Access access = Access.byField(field);
            this.cache.putIfAbsent(context.getAccessKey(), access);
            this.invokePropertyOperation(access, context);
        }
    }

    private void invokePropertyOperation(Access access, CallContext context) {
        context.markSuccess();
        if (context.isRead()) {
            this.invokePropertyGetting(access, context);
        } else {
            this.invokePropertySetting(access, context);
        }
    }

    private void invokePropertySetting(Access access, CallContext context) {
        try {
            if (access.isDirectAccess()) {
                access.getField().set(context.getBean(), context.getFieldValue());
            } else {
                access.getMethod().invoke(context.getBean(), context.getFieldValue());
            }
        }
        catch (Exception ex) {
            logger.warn(String.format("Problem occured setting property %s in class %s", context.getFieldName(), context.getBeanClass()), (Throwable)ex);
        }
    }

    private void invokePropertyGetting(Access access, CallContext context) {
        Object value = null;
        try {
            value = access.isDirectAccess() ? access.getField().get(context.getBean()) : access.getMethod().invoke(context.getBean(), new Object[0]);
        }
        catch (Exception ex) {
            logger.warn(String.format("Problem occured getting property in class %s", context.getBeanClass()), (Throwable)ex);
        }
        context.setValue(value);
    }

    private Field findField(Class<?> clazz, String fieldName) {
        Field field = null;
        while (clazz != null && field == null) {
            try {
                field = clazz.getDeclaredField(fieldName);
            }
            catch (NoSuchFieldException nsex) {
                clazz = clazz.getSuperclass();
            }
            catch (Exception e) {
                break;
            }
        }
        return field;
    }

    void getOrSetPropertyByGetter(CallContext context) {
        Method method = this.prepareReadWriteMethod(context);
        if (method == null) {
            return;
        }
        context.markExecuted();
        Access access = Access.byMethod(method);
        this.cache.putIfAbsent(context.getAccessKey(), access);
        this.invokePropertyOperation(access, context);
    }

    private Method prepareReadWriteMethod(CallContext context) {
        PropertyDescriptor propertyDescriptor = null;
        try {
            propertyDescriptor = new PropertyDescriptor(context.getFieldName(), context.getBeanClass());
        }
        catch (IntrospectionException e) {
            return null;
        }
        if (context.isRead()) {
            return propertyDescriptor.getReadMethod();
        }
        return propertyDescriptor.getWriteMethod();
    }
}
