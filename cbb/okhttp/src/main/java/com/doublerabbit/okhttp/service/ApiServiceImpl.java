package com.doublerabbit.okhttp.service;

import com.doublerabbit.okhttp.model.User;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import java.util.UUID;

public class ApiServiceImpl implements ApiService{

    @Override
    public Mono request(int time) {
        return Mono.fromSupplier(() -> {
            try {
                System.out.println("service1.threadName=" + Thread.currentThread().getName());
                Thread.sleep(time);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            if (time == 3000){
                throw new RuntimeException("param is invalid===");
            }
            return Thread.currentThread().getName() + UUID.randomUUID();
        }).subscribeOn(Schedulers.boundedElastic())
                .map(name -> {
                    return new User(name);
                })      ;
    }

}
