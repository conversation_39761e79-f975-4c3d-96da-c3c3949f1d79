package com.doublerabbit.okhttp.jsonpath.impl;

public class CallContext {
    private Object value;
    private boolean executed;
    private boolean success;
    private final AccessKey accessKey;
    private final Object bean;
    private final Object fieldValue;

    CallContext(Object bean, String fieldName, Object fieldValue) {
        this.bean = bean;
        this.fieldValue = fieldValue;
        this.accessKey = new AccessKey(bean.getClass(), fieldName, AccessKey.AccessType.WRITE);
    }

    CallContext(Object bean, String fieldName) {
        this.bean = bean;
        this.fieldValue = null;
        this.accessKey = new AccessKey(bean.getClass(), fieldName, AccessKey.AccessType.READ);
    }

    Object getValue() {
        return this.value;
    }

    void setValue(Object value) {
        this.value = value;
    }

    AccessKey getAccessKey() {
        return this.accessKey;
    }

    boolean isExecuted() {
        return this.executed;
    }

    boolean isRead() {
        return this.accessKey.getAccessType() == AccessKey.AccessType.READ;
    }

    void markExecuted() {
        this.executed = true;
    }

    void markSuccess() {
        this.success = true;
    }

    public String getFieldName() {
        return this.accessKey.getFieldName();
    }

    public Object getFieldValue() {
        return this.fieldValue;
    }

    public Object getBean() {
        return this.bean;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Class<?> getBeanClass() {
        return this.getBean().getClass();
    }
}
