package com.doublerabbit.okhttp.jsonpath.impl;

import java.util.HashMap;
import java.util.Map;

public abstract class PropertyAccessor<K> {
    public final Object getProperty(Object bean, String propertyName, boolean allowCreate) {
        K sourceBean = this.applyTo(bean);
        if (sourceBean == null) {
            return null;
        }
        return this.doGetProperty(sourceBean, propertyName, allowCreate);
    }

    public final boolean setProperty(Object bean, String propertyName, Object value) {
        K targetBean = this.applyTo(bean);
        if (targetBean == null) {
            return false;
        }
        return this.doSetProperty(targetBean, propertyName, value);
    }

    public final Object createIfNeeded(Map<String, Object> attrs, String propertyName, boolean allowCreate, Object result) {
        if (result == null && allowCreate) {
            result = new HashMap();
            attrs.put(propertyName, result);
        }
        return result;
    }

    public abstract K applyTo(Object var1);

    public abstract Object doGetProperty(K var1, String var2, boolean var3);

    public abstract boolean doSetProperty(K var1, String var2, Object var3);
}
