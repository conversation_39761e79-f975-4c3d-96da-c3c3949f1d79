package com.doublerabbit.okhttp.jsonpath.impl;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

class Access {
    private final Field field;
    private final Method method;

    static Access byField(Field field) {
        return new Access(field, null);
    }

    static Access byMethod(Method method) {
        return new Access(null, method);
    }

    static Access missing() {
        return new Access(null, null);
    }

    private Access(Field field, Method method) {
        this.method = method;
        this.field = field;
    }

    Field getField() {
        return this.field;
    }

    Method getMethod() {
        return this.method;
    }

    boolean isDirectAccess() {
        return this.field != null;
    }

    boolean isByMethodAccess() {
        return this.method != null;
    }

    boolean hasAnyAccess() {
        return this.isDirectAccess() || this.isByMethodAccess();
    }
}
