package com.doublerabbit.okhttp.jsonpath;

import com.doublerabbit.okhttp.jsonpath.impl.AnyObjectPropertyAccessor;
import com.doublerabbit.okhttp.jsonpath.impl.PropertyAccessor;
import com.google.common.base.Splitter;
import org.joda.time.DateTime;

import java.math.BigDecimal;
import java.util.*;

public class JsonPathHelper {
    public static Map<String, Object> getMap(Object bean, String path) {
        return JsonPathHelper.getMap(bean, path, null);
    }

    public static Map<String, Object> getMap(Object bean, String path, Map<String, Object> defaultValue) {
        return JsonPathHelper.getNarrowedObject(bean, path, Map.class, defaultValue);
    }

    public static String getString(Object bean, String path) {
        return JsonPathHelper.getString(bean, path, null);
    }

    public static Boolean getBoolean(Object bean, String path) {
        return JsonPathHelper.getBoolean(bean, path, null);
    }

    @Deprecated
    public static Date getDate(Object bean, String path) {
        return JsonPathHelper.getDate(bean, path, null);
    }

    public static DateTime getDateTime(Object bean, String path) {
        return JsonPathHelper.getDateTime(bean, path, null);
    }

    public static List<?> getList(Object bean, String path) {
        return JsonPathHelper.getList(bean, path, null);
    }

    public static BigDecimal getNumber(Object bean, String path) {
        return JsonPathHelper.getNumber(bean, path, null);
    }

    public static Integer getInteger(Object bean, String path) {
        return JsonPathHelper.getInteger(bean, path, null);
    }

    public static Long getLong(Object bean, String path) {
        return JsonPathHelper.getLong(bean, path, null);
    }

    public static Object getObject(Object bean, String path) {
        return JsonPathHelper.getObject(bean, path, null);
    }

    public static String getString(Object bean, String path, String defaultValue) {
        return JsonPathHelper.getNarrowedObject(bean, path, String.class, defaultValue);
    }

    public static Boolean getBoolean(Object bean, String path, Boolean defaultValue) {
        return JsonPathHelper.getNarrowedObject(bean, path, Boolean.class, defaultValue);
    }

    @Deprecated
    public static Date getDate(Object bean, String path, Date defaultValue) {
        return JsonPathHelper.getNarrowedObject(bean, path, Date.class, defaultValue);
    }

    public static DateTime getDateTime(Object bean, String path, DateTime defaultValue) {
        return JsonPathHelper.getNarrowedObject(bean, path, DateTime.class, defaultValue);
    }

    public static List<?> getList(Object bean, String path, List<?> defaultValue) {
        return JsonPathHelper.getNarrowedObject(bean, path, List.class, defaultValue);
    }

    public static BigDecimal getNumber(Object bean, String path, Number defaultValue) {
        Number result = JsonPathHelper.getNarrowedObject(bean, path, Number.class, defaultValue);
        if (result == null) {
            return null;
        }
        return new BigDecimal(result.toString());
    }

    public static Integer getInteger(Object bean, String path, Integer defaultValue) {
        return JsonPathHelper.getNarrowedObject(bean, path, Integer.class, defaultValue);
    }

    public static Long getLong(Object bean, String path, Long defaultValue) {
        return JsonPathHelper.getNarrowedObject(bean, path, Long.class, defaultValue);
    }

    public static Object getObject(Object bean, String path, Object defaultValue) {
        AccessPathRequest request = AccessPathRequest.pathRequest().intentTo(OperationType.GET).allowCreation(false);
        return JsonPathHelper.getObject(bean, path, defaultValue, request);
    }

    public static Object setValue(Object bean, String path, Object value) {
        AccessPathRequest request = AccessPathRequest.pathRequest().intentTo(OperationType.SET).allowCreation(false);
        return JsonPathHelper.setOrCreateValue(bean, path, value, request);
    }

    public static Object setOrCreateValue(Object bean, String path, Object value) {
        AccessPathRequest request = AccessPathRequest.pathRequest().intentTo(OperationType.SET).allowCreation(true);
        return JsonPathHelper.setOrCreateValue(bean, path, value, request);
    }

    public static Object setValues(Object bean, Object... pathAndValues) {
        AccessPathRequest request = AccessPathRequest.pathRequest().intentTo(OperationType.SET).allowCreation(false);
        return JsonPathHelper.setOrCreateValues(bean, request, pathAndValues);
    }

    public static Object setOrCreateValues(Object bean, Object... pathAndValues) {
        AccessPathRequest request = AccessPathRequest.pathRequest().intentTo(OperationType.SET).allowCreation(true);
        return JsonPathHelper.setOrCreateValues(bean, request, pathAndValues);
    }

    public static Object setOrCreateValues(Object bean, Map<String, Object> pathAndValues) {
        AccessPathRequest request = AccessPathRequest.pathRequest().intentTo(OperationType.SET).allowCreation(true);
        return JsonPathHelper.setOrCreateValues(bean, request, pathAndValues);
    }

    private static Object getObject(Object bean, String path, Object defaultValue, AccessPathRequest request) {
        Iterator<String> pathNodes = Splitter.on(".").omitEmptyStrings().split(path).iterator();
        return JsonPathHelper.getObject(bean, pathNodes, defaultValue, request);
    }

    private static Object setOrCreateValue(Object bean, String path, Object value, AccessPathRequest request) {
        Object target = JsonPathHelper.getObject(bean, path, null, request);
        if (target == null) {
            throw new RuntimeException("There is no object in the path " + path);
        }
        JsonPathHelper.setProperty(target, request.getPropertyToSet(), value);
        return bean;
    }

    private static Object setOrCreateValues(Object bean, AccessPathRequest request, Object... pathAndValues) {
        int totalPathAndValues = pathAndValues.length;
        if (totalPathAndValues % 2 != 0) {
            throw new RuntimeException("setValues(bean, [params,...]) involves even number of params after param 'bean'!");
        }
        for (int i = 0; i < totalPathAndValues; i += 2) {
            String path = (String) pathAndValues[i];
            Object value = pathAndValues[i + 1];
            JsonPathHelper.setOrCreateValue(bean, path, value, request);
        }
        return bean;
    }

    private static Object setOrCreateValues(Object bean, AccessPathRequest request, Map<String, Object> pathAndValues) {
        for (Map.Entry<String, Object> pathAndValue : pathAndValues.entrySet()) {
            JsonPathHelper.setOrCreateValue(bean, pathAndValue.getKey(), pathAndValue.getValue(), request);
        }
        return bean;
    }

    private static <K> K getNarrowedObject(Object bean, String path, Class<K> narrowTo, K defaultValue) {
        return JsonPathHelper.cast(JsonPathHelper.getObject(bean, path, defaultValue), narrowTo);
    }

    private static <K> K cast(Object bean, Class<K> clazz) {
        if (Date.class.equals(clazz)) {
            return (K) JsonPathHelper.castToDate(bean);
        }
        if (DateTime.class.equals(clazz)) {
            return (K) JsonPathHelper.castToDateTime(bean);
        }
        if (String.class.equals(clazz)) {
            return (K) (bean != null ? String.valueOf(bean) : null);
        }
        return (K) bean;
    }

    private static Date castToDate(Object source) {
        if (source == null) {
            return null;
        }
        if (source instanceof DateTime) {
            return ((DateTime) source).toDate();
        }
        return (Date) source;
    }

    private static DateTime castToDateTime(Object source) {
        if (source == null) {
            return null;
        }
        if (source instanceof Date) {
            return new DateTime(((Date) source).getTime());
        }
        return (DateTime) source;
    }

    private static Object getObject(Object bean, Iterator<String> nodeIter, Object defaultValue, AccessPathRequest request) {
        if (!nodeIter.hasNext()) {
            return bean;
        }
        String node = nodeIter.next();
        if (request.isSet() && !nodeIter.hasNext()) {
            request.setPropertyToSet(node);
            return bean;
        }
        if ((bean = JsonPathHelper.getProperty(bean, node, request)) == null) {
            return defaultValue;
        }
        return JsonPathHelper.getObject(bean, nodeIter, defaultValue, request);
    }

    private static Object getProperty(Object bean, String propertyName, AccessPathRequest request) {
        Object result;
        PropertyAccessor<?> propertyAccessor;
        if (bean == null) {
            return null;
        }
        result = null;
        Iterator<PropertyAccessor<?>> i$ = request.getIntent().getAccessors().iterator();
        while (i$.hasNext() && (result = (propertyAccessor = i$.next()).getProperty(bean, propertyName, request.isAllowCreate())) == null) {
        }
        return result;
    }

    private static void setProperty(Object bean, String propertyName, Object value) {
        boolean result;
        PropertyAccessor<?> propertyAccessor;
        Iterator<PropertyAccessor<?>> i$ = OperationType.SET.getAccessors().iterator();
        while (i$.hasNext() && !(result = (propertyAccessor = i$.next()).setProperty(bean, propertyName, value))) {
        }
    }

    public enum OperationType {
        GET(new AnyObjectPropertyAccessor()),
        SET(new AnyObjectPropertyAccessor());

        private final List<PropertyAccessor<?>> accessors;

        OperationType(PropertyAccessor<?>... accessors) {
            this.accessors = Arrays.asList(accessors);
        }

        public List<PropertyAccessor<?>> getAccessors() {
            return this.accessors;
        }
    }

}
