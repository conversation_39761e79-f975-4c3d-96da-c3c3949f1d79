package com.doublerabbit.okhttp.config;

import okhttp3.OkHttpClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;


@ConfigurationProperties(prefix = "okhttp")
@Configuration
public class OkHttpClientAutoConfiguration {

    private Integer conTimeout = 30;
    private Integer writeTimeout = 60;
    private Integer callTimeout = 50;

    @Bean
    public OkHttpClient httpClient(){
       OkHttpClient httpClient = new OkHttpClient().newBuilder()
                .connectTimeout(conTimeout, TimeUnit.SECONDS)
                .writeTimeout(writeTimeout, TimeUnit.SECONDS)
                .callTimeout(callTimeout, TimeUnit.SECONDS)
                .build();
       return httpClient;
    }


}
