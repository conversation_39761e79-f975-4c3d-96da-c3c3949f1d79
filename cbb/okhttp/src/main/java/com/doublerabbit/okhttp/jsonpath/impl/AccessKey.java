package com.doublerabbit.okhttp.jsonpath.impl;

import lombok.EqualsAndHashCode;

@EqualsAndHashCode
public class AccessKey {
    private final Class<?> clazz;
    private final String fieldName;
    private final AccessType accessType;

    AccessKey(Class<?> clazz, String fieldName, AccessType accessType) {
        this.clazz = clazz;
        this.fieldName = fieldName;
        this.accessType = accessType;
    }

    Class<?> getClazz() {
        return this.clazz;
    }

    String getFieldName() {
        return this.fieldName;
    }

    AccessType getAccessType() {
        return this.accessType;
    }


    static enum AccessType {
        READ,
        WRITE;
        

        private AccessType() {
        }
    }

}
