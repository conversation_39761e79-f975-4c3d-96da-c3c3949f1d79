package com.doublerabbit.okhttp.jsonpath.impl;

import java.util.Map;

public class AnyObjectPropertyAccessor extends PropertyAccessor<Object> {
    @Override
    public Object applyTo(Object bean) {
        return bean;
    }

    @Override
    public Object doGetProperty(Object bean, String propertyName, boolean allowCreate) {
        Object result = null;
        Map beanMap = null;
        if (bean instanceof Map) {
            beanMap = (Map) bean;
            result = beanMap.get(propertyName);
        } else {
            result = ReflectionPropertyAccessorHelper.getInstance().getProperty(bean, propertyName);
        }
        if (result == null && beanMap != null) {
            result = this.createIfNeeded(beanMap, propertyName, allowCreate, result);
        }
        return result;
    }

    @Override
    public boolean doSetProperty(Object bean, String propertyName, Object value) {
        if (bean instanceof Map) {
            ((Map) bean).put(propertyName, value);
            return true;
        }
        return ReflectionPropertyAccessorHelper.getInstance().setProperty(bean, propertyName, value);
    }
}
