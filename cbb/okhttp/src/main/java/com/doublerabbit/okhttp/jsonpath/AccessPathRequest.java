package com.doublerabbit.okhttp.jsonpath;

import com.doublerabbit.okhttp.jsonpath.impl.PropertyAccessor;

import java.util.List;

public class AccessPathRequest {
    private JsonPathHelper.OperationType intent;
    private boolean allowCreate;
    private String propertyToSet;

    AccessPathRequest() {
    }

    static AccessPathRequest pathRequest() {
        return new AccessPathRequest();
    }

    public JsonPathHelper.OperationType getIntent() {
        return this.intent;
    }

    public boolean isSet() {
        return this.intent == JsonPathHelper.OperationType.SET;
    }

    public List<PropertyAccessor<?>> getAccessors() {
        return this.intent.getAccessors();
    }

    public boolean isAllowCreate() {
        return this.allowCreate;
    }

    public AccessPathRequest intentTo(JsonPathHelper.OperationType intent) {
        this.intent = intent;
        return this;
    }

    public AccessPathRequest allowCreation(boolean allowCreation) {
        this.allowCreate = allowCreation;
        return this;
    }

    public String getPropertyToSet() {
        return this.propertyToSet;
    }

    public void setPropertyToSet(String propertyToSet) {
        this.propertyToSet = propertyToSet;
    }
}
