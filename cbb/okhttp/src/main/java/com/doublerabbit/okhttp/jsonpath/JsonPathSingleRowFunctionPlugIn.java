package com.doublerabbit.okhttp.jsonpath;

import org.joda.time.DateTime;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


public class JsonPathSingleRowFunctionPlugIn {

    public static String getString(Object bean, String path) {
        return JsonPathHelper.getString(bean, path);
    }

    public static Boolean getBoolean(Object bean, String path) {
        return JsonPathHelper.getBoolean(bean, path);
    }

    @Deprecated
    public static Date getDate(Object bean, String path) {
        return JsonPathHelper.getDate(bean, path);
    }

    public static DateTime getDateTime(Object bean, String path) {
        return JsonPathHelper.getDateTime(bean, path);
    }

    public static List<?> getList(Object bean, String path) {
        return JsonPathHelper.getList(bean, path);
    }

    public static BigDecimal getNumber(Object bean, String path) {
        return JsonPathHelper.getNumber(bean, path);
    }

    public static Object getObject(Object bean, String path) {
        return JsonPathHelper.getObject(bean, path);
    }

    public static String getString(Object bean, String path, String defaultValue) {
        return JsonPathHelper.getString(bean, path, defaultValue);
    }

    public static Boolean getBoolean(Object bean, String path, Boolean defaultValue) {
        return JsonPathHelper.getBoolean(bean, path, defaultValue);
    }

    @Deprecated
    public static Date getDate(Object bean, String path, Date defaultValue) {
        return JsonPathHelper.getDate(bean, path, defaultValue);
    }

    public static DateTime getDateTime(Object bean, String path, DateTime defaultValue) {
        return JsonPathHelper.getDateTime(bean, path, defaultValue);
    }

    public static List<?> getList(Object bean, String path, List<?> defaultValue) {
        return JsonPathHelper.getList(bean, path, defaultValue);
    }

    public static BigDecimal getNumber(Object bean, String path, Number defaultValue) {
        return JsonPathHelper.getNumber(bean, path, defaultValue);
    }

    public static Object getObject(Object bean, String path, Object defaultValue) {
        return JsonPathHelper.getObject(bean, path, defaultValue);
    }

    public static Object setValue(Object bean, String path, Object value) {
        return JsonPathHelper.setValue(bean, path, value);
    }

    public static  Object setValues(Object bean, Object ... pathAndValues) {
        return JsonPathHelper.setValues(bean, pathAndValues);
    }
}
