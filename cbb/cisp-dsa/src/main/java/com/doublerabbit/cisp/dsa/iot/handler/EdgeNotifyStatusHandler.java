package com.doublerabbit.cisp.dsa.iot.handler;

import com.doublerabbit.cisp.dsa.config.SystemConfiguration;
import com.seewo.iot.protocol.SubMessageHandler;
import com.seewo.iot.vo.SubReq;
import com.seewo.iot.vo.SubResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
@Slf4j
public class EdgeNotifyStatusHandler implements SubMessageHandler {
    @Resource
    private SystemConfiguration systemConfiguration;



    @Override
    public void handle(SubReq subReq) {
       log.info("edgeNotifyStatusHandler handle =========");
    }

    @Override
    public SubResp response() {
        return new SubResp();
    }

    @Override
    public String getProductKey() {
        return systemConfiguration.getIotEdgeClientProductKey();
    }

    @Override
    public String getMethod() {
        return "thing.service.edgeNotifyInfo";
    }

//    private void updateDeviceIp(String deviceId, String ip) {
//        DeviceEntity deviceEntity = new DeviceEntity();
//        deviceEntity.setDeviceId(deviceId);
//        deviceEntity.setIp(ip);
//        String rtsp = Constants.GBS_LIVE_RTSP_PREFIX + ip + ":554/1";
//        DeviceCreateResp deviceCreateResp = videoServiceApi.applyGbsId(Constants.GBS_PARAM_LIVE);
//        if (Objects.nonNull(deviceCreateResp)) {
//            String gbsId = deviceCreateResp.getIpcList().get(0).getDeviceId();
//            //向gbs注册rtsp流与gbsId的组合
//            Boolean isSuccess = videoServiceApi.addToGbs(deviceCreateResp.getIpcList().get(0).getDeviceId(), rtsp, Constants.GBS_PARAM_LIVE);
//            if (!isSuccess) {
//                log.error("{},{} add to gbs failure", gbsId, rtsp);
//            } else {
//                log.info("{},{} add to gbs success", gbsId, rtsp);
//                deviceEntity.setGbsId(gbsId);
//            }
//        }
//        deviceDomainService.updateByDeviceId(deviceEntity);
//    }
//
//    private void updateDeviceStatus(String deviceId, String deviceStatus, String deviceType) {
//        updateDeviceStatus(deviceId, deviceStatus);
//        DetailInfoVo detailInfoVo = new DetailInfoVo().setDeviceId(deviceId).setDeviceStatus(deviceStatus);
//        if (DeviceTypeEnum.RECORDER.getDesc().equals(deviceType)) {
//            List<DeviceEntity> deviceEntities = deviceDomainService.findByGbsIdOrTwinSnOrDeviceId(deviceId);
//            if (CollectionUtils.isNotEmpty(deviceEntities)) {
//                detailInfoVo.setDeviceId(deviceEntities.get(0).getSn());
//            }
//        }
//        log.info("cloud-iot notify edge deviceId {},status {}", deviceId, deviceStatus);
//        twinApiService.attributeUpdate(detailInfoVo);
//    }

}
