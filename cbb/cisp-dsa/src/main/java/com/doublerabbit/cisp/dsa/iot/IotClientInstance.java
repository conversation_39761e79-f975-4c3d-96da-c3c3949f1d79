package com.doublerabbit.cisp.dsa.iot;

import com.seewo.iot.IotClient;
import com.seewo.iot.IotService;

public class IotClientInstance {

    private IotClient iotClient;

    public IotClient getIotClient() {
        return iotClient;
    }

    public void setIotClient(IotClient iotClient) {
        this.iotClient = iotClient;
    }

    private static class SingletonInstance {
        private static final IotClientInstance instance = new IotClientInstance();
    }

    public static IotClientInstance getInstance(){
       return SingletonInstance.instance;
    }

    private IotClientInstance(){

    }

    /**
     * 创建IotClient
     */
    public void initIotClient(String registerUrl,
                             String broker,
                             String grailUrl,
                             String grailEnv,
                             Class<? extends IotService> nodeType,
                             Boolean debugMode) {
        this.iotClient = IotClient.create()
                .remote(broker)
                .nodeType(nodeType)
                .grail(grailUrl,grailEnv)
                .registerUrl(registerUrl)
                .debugMode(debugMode)
                .build();
    }
}
