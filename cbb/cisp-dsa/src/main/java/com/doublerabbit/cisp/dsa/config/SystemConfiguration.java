package com.doublerabbit.cisp.dsa.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 系统信息配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "system")
public class SystemConfiguration {

    private String schoolId = "";

    private Iot iot = new Iot();

    public String getIotAccessKey(){
        return iot.getAccessKey();
    }
    public String getIotSecret(){
        return iot.getSecretKey();
    }

    public String getIotEdgeClientProductKey() {
        return iot.getEdgeClientProductKey();
    }

    public String getIotEdgeClientSecret() {
        return iot.getEdgeClientSecret();
    }

    public String getIotBroker(){
        return iot.getBroker();
    }

    public String getRegisterUrl(){
        return iot.getRegisterUrl();
    }

    public String getGrailUrl() {
        return iot.getGrailUrl();
    }

    public String getGrailEnv(){
        return iot.getGrailEnv();
    }

    @Data
    public class Iot{
        private String host = "";
        private String accessKey = "";
        private String secretKey = "";
        private String deviceType="";
        private String broker = "";
        private String edgeClientProductKey = "";
        private String edgeClientSecret = "";
        private String registerUrl = "";
        private String grailUrl = "";
        private String grailEnv = "";
        /** 边缘客户端设备ID是否自动加入分组 */
        private Boolean autoToGroup = false;
    }

}
