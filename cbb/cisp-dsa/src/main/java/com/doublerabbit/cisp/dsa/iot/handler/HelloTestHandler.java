package com.doublerabbit.cisp.dsa.iot.handler;

import com.alibaba.fastjson.JSONObject;
import com.doublerabbit.cisp.dsa.config.SystemConfiguration;
import com.seewo.iot.protocol.SubMessageHandler;
import com.seewo.iot.vo.SubReq;
import com.seewo.iot.vo.SubResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


@Component
@Slf4j
public class HelloTestHandler implements SubMessageHandler {
    @Resource
    private SystemConfiguration systemConfiguration;

    private Map data = new HashMap();


    @Override
    public void handle(SubReq subReq) {
       log.info("HelloTestHandler handle =========, {}", JSONObject.toJSONString(subReq));
        data.put("hello", "world");
    }

    @Override
    public SubResp response() {
        SubResp resp = new SubResp();
        resp.setData(data);
        return resp;
    }

    @Override
    public String getProductKey() {
        return systemConfiguration.getIotEdgeClientProductKey();
    }

    @Override
    public String getMethod() {
        return "thing.service.getHelloDesc";
    }


}
