package com.doublerabbit.cisp.dsa.iot;

import com.doublerabbit.cisp.dsa.config.SystemConfiguration;
import com.doublerabbit.cisp.dsa.iot.handler.EdgeNotifyStatusHandler;
import com.doublerabbit.cisp.dsa.iot.handler.HelloTestHandler;
import com.seewo.iot.IotClient;
import com.seewo.iot.IotMqttService;
import com.seewo.iot.common.IotException;
import com.seewo.iot.vo.register.RegisterReq;
import com.seewo.iot.vo.register.RegisterResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;


//@ConditionalOnProperty(name = {"system.deploy.edge", "system.iot.sync"}, havingValue = "true")
@Service
@Slf4j
public class EdgeIotClientService implements ApplicationListener<ApplicationStartedEvent> {
    @Resource
    private SystemConfiguration systemConfig;

    @Resource
    private EdgeNotifyStatusHandler edgeNotifyStatusHandler;

    @Resource
    private HelloTestHandler helloTestHandler;



    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        log.info("<<<<<<<<<<<<<Application start success,begin to start iot client>>>>>>>>>>>>>>>");
        if(StringUtils.isBlank(systemConfig.getSchoolId())){
            log.error("Not set school");
            return;
        }
        IotClientInstance instance = IotClientInstance.getInstance();
        instance.initIotClient(systemConfig.getRegisterUrl(),
                systemConfig.getIotBroker(),
                systemConfig.getGrailUrl(),
                systemConfig.getGrailEnv(),
                IotMqttService.class,
                false);
        IotClient iotClient = instance.getIotClient();
        RegisterResp registerResp = registerToIot(systemConfig.getSchoolId(), iotClient);
        if(!registerResp.success()){
            log.warn("iot-edge-client {} register failure", systemConfig.getSchoolId());
            return;
        }
        IotMqttService mqttService = null;
        try {
            mqttService = (IotMqttService) iotClient.auth(systemConfig.getIotEdgeClientProductKey(),
                            registerResp.getData().getDeviceId(),registerResp.getData().getDeviceSecret()).start();
            //下行处理器注册，边缘每添加一个处理器都需要在这里注册
            String iotDeviceId = registerResp.getData().getDeviceId();
            mqttService.registerSubHandler(Arrays.asList(edgeNotifyStatusHandler, helloTestHandler));
            log.info("下行处理器注册成功");
//            ProjectVo projectVo =  projectApiService.getCurrentProject();
//            projectApiService.syncEdgeDeviceIdToCloud(iotDeviceId, projectVo.getId());
            log.info("同步边缘iot设备id到云端成功，edgeDeviceId:{}", iotDeviceId);
//            // 添加至数字孪生边缘物联客户端的分组中，如果是自动清加则需要向集控申请权限，如果不是自动，则手动在iot平台上将设备ID添加至分组中
//            if (systemConfig.getIot().getAutoToGroup()){
//                ucpOpenApiAclService.addDevicesToGroup(projectVo.getGroupId(), Lists.newArrayList(iotDeviceId), systemConfig.getIotEdgeClientProductKey(), systemConfig.getDeploySchool(), Constants.IOT_TAG_KEY);
//            }
        } catch (IotException e) {
            log.error("iot client start error {}",e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private RegisterResp registerToIot(String schoolCode,IotClient iotClient){
        RegisterReq.Identity identity = new RegisterReq.Identity();
        Set<String> values = new HashSet<>();
        values.add(schoolCode);
        identity.setType("uid").setValues(values);
        RegisterResp register = null;
        try {
            register = iotClient.register(systemConfig.getIotEdgeClientProductKey(), systemConfig.getIotEdgeClientSecret(), Arrays.asList(identity));
        } catch (IotException e) {
            throw new RuntimeException(e);
        }
        return register;
    }
}
