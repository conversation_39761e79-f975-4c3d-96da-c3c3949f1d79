package com.doublerabbit.enable;

import com.doublerabbit.tool.EnableHelloWorld;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * Hello world!
 *
 */
@SpringBootApplication
public class EnableApp
{
    public static void main( String[] args )
    {
//        System.out.println("enable pattern test=====");
//        SpringApplication.run(EnableApp.class, args);
        ConfigurableApplicationContext context = new SpringApplicationBuilder(EnableApp.class)
                .web(WebApplicationType.NONE)
                .run(args);

        // helloWorld Bean 是否存在
        String helloWorld =
                context.getBean("helloWorld", String.class);

        System.out.println("helloWorld Bean : " + helloWorld);

        // 关闭上下文
        context.close();
    }
}
