<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.doublerabbit</groupId>
    <artifactId>easy-java-bom</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
        <fastjson.version>1.2.83</fastjson.version>
        <flyway.version>8.2.0</flyway.version>
        <p6spy.version>3.9.1</p6spy.version>
        <mysql.connector.version>8.0.30</mysql.connector.version>
        <okhttp.version>3.14.9</okhttp.version>
        <feign.version>11.9.1</feign.version>
        <iot.sdk.version>1.1-SNAPSHOT</iot.sdk.version>
        <iot.service.version>3.1.9-SNAPSHOT</iot.service.version>
        <iot.register.version>2.0.0-SNAPSHOT</iot.register.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.5.6</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flyway.version}</version>
            </dependency>
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>2.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-core</artifactId>
                <version>${feign.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-gson</artifactId>
                <version>${feign.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.yomahub</groupId>-->
<!--                <artifactId>liteflow-spring-boot-starter</artifactId>-->
<!--                <version>2.10.6</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.seewo.iot</groupId>-->
<!--                <artifactId>seewo-iot-sdk-java</artifactId>-->
<!--                <version>${iot.sdk.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.seewo.iot.platform</groupId>-->
<!--                <artifactId>iot-platform-service-api</artifactId>-->
<!--                <version>${iot.service.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.seewo.iot.platform.register</groupId>-->
<!--                <artifactId>iot-platform-register-service-api</artifactId>-->
<!--                <version>${iot.register.version}</version>-->
<!--            </dependency>-->
        </dependencies>
    </dependencyManagement>

    <repositories>

    </repositories>
    <distributionManagement>

    </distributionManagement>
</project>