1674406001636|8|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1674406001684|40|statement|connection 0|url ******************************************************************************************************************************************************************************|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'
1674406001687|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY
1674406001690|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1674406001700|8|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406001704|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@foreign_key_checks|SELECT @@foreign_key_checks
1674406001706|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@sql_safe_updates|SELECT @@sql_safe_updates
1674406001707|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1674406001709|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1674406001711|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1674406001712|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1674406001713|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1674406001722|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406001724|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406001736|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT COUNT(1) FROM information_schema.schemata WHERE schema_name=? LIMIT 1|SELECT COUNT(1) FROM information_schema.schemata WHERE schema_name='flyway' LIMIT 1
1674406001744|7|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUM(found) FROM ((SELECT 1 as found FROM information_schema.tables WHERE table_schema=?) UNION ALL (SELECT 1 as found FROM information_schema.views WHERE table_schema=? LIMIT 1) UNION ALL (SELECT 1 as found FROM information_schema.table_constraints WHERE table_schema=? LIMIT 1) UNION ALL (SELECT 1 as found FROM information_schema.triggers WHERE event_object_schema=?  LIMIT 1) UNION ALL (SELECT 1 as found FROM information_schema.routines WHERE routine_schema=? LIMIT 1) UNION ALL (SELECT 1 as found FROM information_schema.events WHERE event_schema=? LIMIT 1)) as all_found|SELECT SUM(found) FROM ((SELECT 1 as found FROM information_schema.tables WHERE table_schema='flyway') UNION ALL (SELECT 1 as found FROM information_schema.views WHERE table_schema='flyway' LIMIT 1) UNION ALL (SELECT 1 as found FROM information_schema.table_constraints WHERE table_schema='flyway' LIMIT 1) UNION ALL (SELECT 1 as found FROM information_schema.triggers WHERE event_object_schema='flyway'  LIMIT 1) UNION ALL (SELECT 1 as found FROM information_schema.routines WHERE routine_schema='flyway' LIMIT 1) UNION ALL (SELECT 1 as found FROM information_schema.events WHERE event_schema='flyway' LIMIT 1)) as all_found
1674406001754|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT COUNT(1) FROM information_schema.schemata WHERE schema_name=? LIMIT 1|SELECT COUNT(1) FROM information_schema.schemata WHERE schema_name='flyway' LIMIT 1
1674406001755|1|commit|connection 0|url ******************************************************************************************************************************************************************************||
1674406001762|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1674406001764|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406001765|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406001860|63|statement|connection 0|url ******************************************************************************************************************************************************************************|CREATE TABLE `flyway`.`flyway_schema_history` (     `installed_rank` INT NOT NULL,     `version` VARCHAR(50),     `description` VARCHAR(200) NOT NULL,     `type` VARCHAR(20) NOT NULL,     `script` VARCHAR(1000) NOT NULL,     `checksum` INT,     `installed_by` VARCHAR(100) NOT NULL,     `installed_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,     `execution_time` INT NOT NULL,     `success` BOOL NOT NULL,     CONSTRAINT `flyway_schema_history_pk` PRIMARY KEY (`installed_rank`) ) ENGINE=InnoDB|CREATE TABLE `flyway`.`flyway_schema_history` (     `installed_rank` INT NOT NULL,     `version` VARCHAR(50),     `description` VARCHAR(200) NOT NULL,     `type` VARCHAR(20) NOT NULL,     `script` VARCHAR(1000) NOT NULL,     `checksum` INT,     `installed_by` VARCHAR(100) NOT NULL,     `installed_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,     `execution_time` INT NOT NULL,     `success` BOOL NOT NULL,     CONSTRAINT `flyway_schema_history_pk` PRIMARY KEY (`installed_rank`) ) ENGINE=InnoDB
1674406001895|34|statement|connection 0|url ******************************************************************************************************************************************************************************|CREATE INDEX `flyway_schema_history_s_idx` ON `flyway`.`flyway_schema_history` (`success`)|CREATE INDEX `flyway_schema_history_s_idx` ON `flyway`.`flyway_schema_history` (`success`)
1674406001897|0|commit|connection 0|url ******************************************************************************************************************************************************************************||
1674406001899|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406001900|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406001904|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1674406001907|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406001909|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406001911|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1674406001917|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406001919|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406001923|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1674406001936|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406001937|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406001939|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT COUNT(1) FROM information_schema.schemata WHERE schema_name=? LIMIT 1|SELECT COUNT(1) FROM information_schema.schemata WHERE schema_name='flyway' LIMIT 1
1674406001942|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1674406001987|43|statement|connection 0|url ******************************************************************************************************************************************************************************|CREATE TABLE project (                          id BIGINT NOT NULL COMMENT '主键',                          name VARCHAR(32) NOT NULL COMMENT '项目名称',                          cid VARCHAR(64) NOT NULL COMMENT '课件ID',                          group_id VARCHAR(32) NOT NULL COMMENT '项目归属组织ID',                          ext VARCHAR(255) NULL COMMENT '扩展字段项',                          thumbnail VARCHAR(255) NULL COMMENT '缩略图',                          description VARCHAR(1024) NULL COMMENT '项目描述',                          is_delete tinyint DEFAULT 0 COMMENT '是否删除',                          version INT NOT NULL DEFAULT 1 COMMENT '版本',                          created_at BIGINT(20) UNSIGNED NOT NULL COMMENT '创建时间',                          created_by VARCHAR(32) NULL COMMENT '创建用户',                          updated_at BIGINT(20) UNSIGNED NOT NULL COMMENT '最后更新时间',                          updated_by VARCHAR(32) NULL COMMENT '最后更新用户',                          PRIMARY KEY (id) ) COMMENT='可视化看板项目表'|CREATE TABLE project (                          id BIGINT NOT NULL COMMENT '主键',                          name VARCHAR(32) NOT NULL COMMENT '项目名称',                          cid VARCHAR(64) NOT NULL COMMENT '课件ID',                          group_id VARCHAR(32) NOT NULL COMMENT '项目归属组织ID',                          ext VARCHAR(255) NULL COMMENT '扩展字段项',                          thumbnail VARCHAR(255) NULL COMMENT '缩略图',                          description VARCHAR(1024) NULL COMMENT '项目描述',                          is_delete tinyint DEFAULT 0 COMMENT '是否删除',                          version INT NOT NULL DEFAULT 1 COMMENT '版本',                          created_at BIGINT(20) UNSIGNED NOT NULL COMMENT '创建时间',                          created_by VARCHAR(32) NULL COMMENT '创建用户',                          updated_at BIGINT(20) UNSIGNED NOT NULL COMMENT '最后更新时间',                          updated_by VARCHAR(32) NULL COMMENT '最后更新用户',                          PRIMARY KEY (id) ) COMMENT='可视化看板项目表'
1674406001999|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406002001|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406002009|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1674406002011|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406002012|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406002017|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT * FROM `flyway`.`flyway_schema_history` FOR UPDATE|SELECT * FROM `flyway`.`flyway_schema_history` FOR UPDATE
1674406002020|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1674406002023|3|statement|connection 0|url ******************************************************************************************************************************************************************************|INSERT INTO `flyway`.`flyway_schema_history` (`installed_rank`, `version`, `description`, `type`, `script`, `checksum`, `installed_by`, `execution_time`, `success`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)|INSERT INTO `flyway`.`flyway_schema_history` (`installed_rank`, `version`, `description`, `type`, `script`, `checksum`, `installed_by`, `execution_time`, `success`) VALUES (1, '*******', 'init', 'SQL', 'V1_0_0_1__init.sql', -1248657341, 'root', 60, true)
1674406002032|4|commit|connection 0|url ******************************************************************************************************************************************************************************||
1674406002039|4|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1674406002041|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406002042|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406002044|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1674406002045|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406002046|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406002051|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1674406002055|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1674406002057|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406002059|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406002063|1|commit|connection 0|url ******************************************************************************************************************************************************************************||
1674406210438|6|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1674406210457|12|statement|connection 0|url ******************************************************************************************************************************************************************************|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'
1674406210458|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY
1674406210462|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1674406210465|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406210466|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@foreign_key_checks|SELECT @@foreign_key_checks
1674406210470|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@sql_safe_updates|SELECT @@sql_safe_updates
1674406210472|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1674406210474|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1674406210476|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1674406210477|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1674406210479|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1674406210486|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406210489|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406210502|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406210504|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406210506|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1674406210516|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406210517|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406210521|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1674406210553|4|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406210555|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406210559|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT COUNT(1) FROM information_schema.schemata WHERE schema_name=? LIMIT 1|SELECT COUNT(1) FROM information_schema.schemata WHERE schema_name='flyway' LIMIT 1
1674406210563|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1674406210612|46|statement|connection 0|url ******************************************************************************************************************************************************************************|alter table project add column note varchar(64) null comment '备注' after description|alter table project add column note varchar(64) null comment '备注' after description
1674406210615|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406210616|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406210620|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > 1 ORDER BY `installed_rank`
1674406210622|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406210623|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406210627|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT * FROM `flyway`.`flyway_schema_history` FOR UPDATE|SELECT * FROM `flyway`.`flyway_schema_history` FOR UPDATE
1674406210629|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1674406210632|1|statement|connection 0|url ******************************************************************************************************************************************************************************|INSERT INTO `flyway`.`flyway_schema_history` (`installed_rank`, `version`, `description`, `type`, `script`, `checksum`, `installed_by`, `execution_time`, `success`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)|INSERT INTO `flyway`.`flyway_schema_history` (`installed_rank`, `version`, `description`, `type`, `script`, `checksum`, `installed_by`, `execution_time`, `success`) VALUES (2, '*******', 'alter project', 'SQL', 'V1_0_0_2__alter_project.sql', 1386682370, 'root', 64, true)
1674406210641|5|commit|connection 0|url ******************************************************************************************************************************************************************************||
1674406210644|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1674406210646|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406210647|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406210649|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1674406210651|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406210653|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406210658|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > 1 ORDER BY `installed_rank`
1674406210660|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1674406210663|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1674406210664|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1674406210668|0|commit|connection 0|url ******************************************************************************************************************************************************************************||
1675750526618|8|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1675750526770|144|statement|connection 0|url ******************************************************************************************************************************************************************************|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'
1675750526774|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY
1675750526779|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1675750526800|19|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1675750526805|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@foreign_key_checks|SELECT @@foreign_key_checks
1675750526807|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@sql_safe_updates|SELECT @@sql_safe_updates
1675750526809|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1675750526811|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1675750526813|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1675750526815|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1675750526816|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1675750526825|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1675750526827|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1675750526851|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1675750526852|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1675750526856|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1675750526932|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1675750526934|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1675750526985|47|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1675750526991|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1675750526992|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1675750526993|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1675750527001|1|commit|connection 0|url ******************************************************************************************************************************************************************************||
1676168121585|6|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1676168121712|121|statement|connection 0|url ******************************************************************************************************************************************************************************|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'
1676168121714|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY
1676168121722|5|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1676168121743|19|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1676168121747|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@foreign_key_checks|SELECT @@foreign_key_checks
1676168121751|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@sql_safe_updates|SELECT @@sql_safe_updates
1676168121753|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1676168121755|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1676168121757|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1676168121764|4|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1676168121765|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1676168121774|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1676168121777|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1676168121875|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1676168121876|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1676168121881|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1676168121962|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1676168121963|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1676168121973|6|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1676168121981|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1676168121982|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1676168121983|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1676168121992|1|commit|connection 0|url ******************************************************************************************************************************************************************************||
1676174866770|7|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1676174866815|6|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name=?|SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name='flyway'
1676174866836|5|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1676174866840|1|commit|connection 9|url ******************************************************************************************************************************************************************************||
1676174866845|0|commit|connection 9|url ******************************************************************************************************************************************************************************||
1676175089834|9|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1676175089859|3|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name=?|SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name='flyway'
1676175089880|3|commit|connection 9|url ******************************************************************************************************************************************************************************||
1676175089887|2|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name=?|SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name='flyway'
1676175089898|10|commit|connection 9|url ******************************************************************************************************************************************************************************||
1676175089918|13|statement|connection 9|url ******************************************************************************************************************************************************************************|Select (Select count(*) from information_schema.TABLES Where TABLE_SCHEMA=?) + (Select count(*) from information_schema.VIEWS Where TABLE_SCHEMA=?) + (Select count(*) from information_schema.TABLE_CONSTRAINTS Where TABLE_SCHEMA=?) + (Select count(*) from information_schema.EVENTS Where EVENT_SCHEMA=?) + (Select count(*) from information_schema.TRIGGERS Where TRIGGER_SCHEMA=?) + (Select count(*) from information_schema.ROUTINES Where ROUTINE_SCHEMA=?)|Select (Select count(*) from information_schema.TABLES Where TABLE_SCHEMA='flyway') + (Select count(*) from information_schema.VIEWS Where TABLE_SCHEMA='flyway') + (Select count(*) from information_schema.TABLE_CONSTRAINTS Where TABLE_SCHEMA='flyway') + (Select count(*) from information_schema.EVENTS Where EVENT_SCHEMA='flyway') + (Select count(*) from information_schema.TRIGGERS Where TRIGGER_SCHEMA='flyway') + (Select count(*) from information_schema.ROUTINES Where ROUTINE_SCHEMA='flyway')
1676175090009|71|statement|connection 9|url ******************************************************************************************************************************************************************************|CREATE TABLE `flyway`.`flyway_schema_history` (     `installed_rank` INT NOT NULL,     `version` VARCHAR(50),     `description` VARCHAR(200) NOT NULL,     `type` VARCHAR(20) NOT NULL,     `script` VARCHAR(1000) NOT NULL,     `checksum` INT,     `installed_by` VARCHAR(100) NOT NULL,     `installed_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,     `execution_time` INT NOT NULL,     `success` BOOL NOT NULL,     -- Add the primary key as part of the CREATE TABLE statement in case `innodb_force_primary_key` is enabled     CONSTRAINT `flyway_schema_history_pk`PRIMARY KEY (`installed_rank`) ) ENGINE=InnoDB|CREATE TABLE `flyway`.`flyway_schema_history` (     `installed_rank` INT NOT NULL,     `version` VARCHAR(50),     `description` VARCHAR(200) NOT NULL,     `type` VARCHAR(20) NOT NULL,     `script` VARCHAR(1000) NOT NULL,     `checksum` INT,     `installed_by` VARCHAR(100) NOT NULL,     `installed_on` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,     `execution_time` INT NOT NULL,     `success` BOOL NOT NULL,     -- Add the primary key as part of the CREATE TABLE statement in case `innodb_force_primary_key` is enabled     CONSTRAINT `flyway_schema_history_pk`PRIMARY KEY (`installed_rank`) ) ENGINE=InnoDB
1676175090059|48|statement|connection 9|url ******************************************************************************************************************************************************************************|CREATE INDEX `flyway_schema_history_s_idx` ON `flyway`.`flyway_schema_history` (`success`)|CREATE INDEX `flyway_schema_history_s_idx` ON `flyway`.`flyway_schema_history` (`success`)
1676175090060|0|commit|connection 9|url ******************************************************************************************************************************************************************************||
1676175090069|4|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1676175090077|5|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1676175090086|1|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name=?|SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name='flyway'
1676175090132|43|statement|connection 9|url ******************************************************************************************************************************************************************************|CREATE TABLE project (                          id BIGINT NOT NULL COMMENT '主键',                          name VARCHAR(32) NOT NULL COMMENT '项目名称',                          cid VARCHAR(64) NOT NULL COMMENT '课件ID',                          group_id VARCHAR(32) NOT NULL COMMENT '项目归属组织ID',                          ext VARCHAR(255) NULL COMMENT '扩展字段项',                          thumbnail VARCHAR(255) NULL COMMENT '缩略图',                          description VARCHAR(1024) NULL COMMENT '项目描述',                          is_delete tinyint DEFAULT 0 COMMENT '是否删除',                          version INT NOT NULL DEFAULT 1 COMMENT '版本',                          created_at BIGINT(20) UNSIGNED NOT NULL COMMENT '创建时间',                          created_by VARCHAR(32) NULL COMMENT '创建用户',                          updated_at BIGINT(20) UNSIGNED NOT NULL COMMENT '最后更新时间',                          updated_by VARCHAR(32) NULL COMMENT '最后更新用户',                          PRIMARY KEY (id) ) COMMENT='可视化看板项目表'|CREATE TABLE project (                          id BIGINT NOT NULL COMMENT '主键',                          name VARCHAR(32) NOT NULL COMMENT '项目名称',                          cid VARCHAR(64) NOT NULL COMMENT '课件ID',                          group_id VARCHAR(32) NOT NULL COMMENT '项目归属组织ID',                          ext VARCHAR(255) NULL COMMENT '扩展字段项',                          thumbnail VARCHAR(255) NULL COMMENT '缩略图',                          description VARCHAR(1024) NULL COMMENT '项目描述',                          is_delete tinyint DEFAULT 0 COMMENT '是否删除',                          version INT NOT NULL DEFAULT 1 COMMENT '版本',                          created_at BIGINT(20) UNSIGNED NOT NULL COMMENT '创建时间',                          created_by VARCHAR(32) NULL COMMENT '创建用户',                          updated_at BIGINT(20) UNSIGNED NOT NULL COMMENT '最后更新时间',                          updated_by VARCHAR(32) NULL COMMENT '最后更新用户',                          PRIMARY KEY (id) ) COMMENT='可视化看板项目表'
1676175090144|1|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1676175090147|1|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT * FROM `flyway`.`flyway_schema_history` FOR UPDATE|SELECT * FROM `flyway`.`flyway_schema_history` FOR UPDATE
1676175090156|8|statement|connection 9|url ******************************************************************************************************************************************************************************|INSERT INTO `flyway`.`flyway_schema_history` (`installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_by`,`execution_time`,`success`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)|INSERT INTO `flyway`.`flyway_schema_history` (`installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_by`,`execution_time`,`success`) VALUES (1, '*******', 'init', 'SQL', 'V1_0_0_1__init.sql', -97773380, 'root', 54, true)
1676175090162|5|commit|connection 9|url ******************************************************************************************************************************************************************************||
1676175090165|1|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1676175090167|1|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1676175090172|2|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1676175090180|1|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name=?|SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name='flyway'
1676175090206|24|statement|connection 9|url ******************************************************************************************************************************************************************************|alter table project add column note varchar(64) null comment '备注' after description|alter table project add column note varchar(64) null comment '备注' after description
1676175090210|1|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > 1 ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > 1 ORDER BY `installed_rank`
1676175090212|1|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT * FROM `flyway`.`flyway_schema_history` FOR UPDATE|SELECT * FROM `flyway`.`flyway_schema_history` FOR UPDATE
1676175090215|3|statement|connection 9|url ******************************************************************************************************************************************************************************|INSERT INTO `flyway`.`flyway_schema_history` (`installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_by`,`execution_time`,`success`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)|INSERT INTO `flyway`.`flyway_schema_history` (`installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_by`,`execution_time`,`success`) VALUES (2, '*******', 'alter project', 'SQL', 'V1_0_0_2__alter_project.sql', 1386682370, 'root', 29, true)
1676175090222|6|commit|connection 9|url ******************************************************************************************************************************************************************************||
1676175090225|1|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1676175090227|1|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1676175090230|1|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > 1 ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > 1 ORDER BY `installed_rank`
1676175090234|1|statement|connection 9|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1676175090238|1|commit|connection 9|url ******************************************************************************************************************************************************************************||
1677816713325|10|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1677816713534|199|statement|connection 0|url ******************************************************************************************************************************************************************************|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'
1677816713537|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY
1677816713542|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677816713553|10|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677816713556|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@foreign_key_checks|SELECT @@foreign_key_checks
1677816713557|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@sql_safe_updates|SELECT @@sql_safe_updates
1677816713559|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677816713561|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677816713563|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677816713568|4|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1677816713570|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1677816713648|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677816713651|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677816713736|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677816713737|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677816713741|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1677816713753|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677816713755|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677816713765|7|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1677816713771|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1677816713772|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677816713773|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677816713777|1|commit|connection 0|url ******************************************************************************************************************************************************************************||
1677816752502|18|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1677816752591|80|statement|connection 0|url ******************************************************************************************************************************************************************************|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'
1677816752593|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY
1677816752597|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677816752600|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677816752602|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@foreign_key_checks|SELECT @@foreign_key_checks
1677816752606|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@sql_safe_updates|SELECT @@sql_safe_updates
1677816752608|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677816752611|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677816752615|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677816752616|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1677816752618|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1677816752689|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677816752693|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677816752732|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677816752733|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677816752735|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1677816752747|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677816752748|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677816752753|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1677816752759|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1677816752761|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677816752762|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677816752767|0|commit|connection 0|url ******************************************************************************************************************************************************************************||
1677817211135|9|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1677817211207|64|statement|connection 0|url ******************************************************************************************************************************************************************************|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'
1677817211209|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY
1677817211212|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677817211216|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677817211221|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@foreign_key_checks|SELECT @@foreign_key_checks
1677817211223|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@sql_safe_updates|SELECT @@sql_safe_updates
1677817211226|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677817211229|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677817211231|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677817211233|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1677817211236|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1677817211315|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677817211318|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677817211344|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677817211345|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677817211348|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1677817211359|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677817211360|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677817211365|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1677817211370|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1677817211372|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677817211372|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677817211376|0|commit|connection 0|url ******************************************************************************************************************************************************************************||
1677847333272|9|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1677847333492|212|statement|connection 0|url ******************************************************************************************************************************************************************************|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'
1677847333494|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY
1677847333498|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677847333512|13|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677847333517|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@foreign_key_checks|SELECT @@foreign_key_checks
1677847333519|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@sql_safe_updates|SELECT @@sql_safe_updates
1677847333522|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677847333524|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677847333526|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677847333546|18|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1677847333552|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1677847333569|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677847333633|62|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677847333740|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677847333742|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677847333747|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1677847333758|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677847333759|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677847333770|7|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1677847333776|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1677847333777|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677847333778|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677847333782|0|commit|connection 0|url ******************************************************************************************************************************************************************************||
1677849533539|9|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1677849533626|78|statement|connection 0|url ******************************************************************************************************************************************************************************|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'
1677849533629|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY
1677849533633|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677849533637|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677849533644|4|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@foreign_key_checks|SELECT @@foreign_key_checks
1677849533647|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@sql_safe_updates|SELECT @@sql_safe_updates
1677849533648|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677849533652|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677849533654|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677849533655|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1677849533657|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1677849533664|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677849533667|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677849533772|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677849533774|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677849533777|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1677849533790|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677849533791|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677849533800|5|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1677849533805|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1677849533806|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677849533807|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677849533811|1|commit|connection 0|url ******************************************************************************************************************************************************************************||
1677893272179|7|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1677893272249|63|statement|connection 0|url ******************************************************************************************************************************************************************************|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'
1677893272251|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY
1677893272259|6|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677893272263|4|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677893272268|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@foreign_key_checks|SELECT @@foreign_key_checks
1677893272270|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@sql_safe_updates|SELECT @@sql_safe_updates
1677893272271|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677893272275|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677893272277|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1677893272283|4|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1677893272285|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1677893272358|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677893272363|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677893272416|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677893272417|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677893272420|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1677893272430|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677893272431|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677893272443|4|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1677893272449|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1677893272450|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1677893272452|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1677893272457|1|commit|connection 0|url ******************************************************************************************************************************************************************************||
1678065419668|7|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1678065419766|89|statement|connection 0|url ******************************************************************************************************************************************************************************|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'
1678065419767|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY
1678065419771|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1678065419776|4|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1678065419784|4|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@foreign_key_checks|SELECT @@foreign_key_checks
1678065419787|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@sql_safe_updates|SELECT @@sql_safe_updates
1678065419792|4|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1678065419794|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1678065419796|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1678065419800|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1678065419802|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1678065419810|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1678065419872|60|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1678065419950|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1678065419951|0|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1678065419954|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1678065419966|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1678065419968|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1678065419980|8|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1678065419986|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1678065419988|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1678065419989|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1678065419994|1|commit|connection 0|url ******************************************************************************************************************************************************************************||
1678074229716|11|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1678074229784|60|statement|connection 0|url ******************************************************************************************************************************************************************************|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'|select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'
1678074229786|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY|SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY
1678074229789|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1678074229793|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1678074229797|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@foreign_key_checks|SELECT @@foreign_key_checks
1678074229799|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT @@sql_safe_updates|SELECT @@sql_safe_updates
1678074229801|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1678074229803|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1678074229805|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT DATABASE()|SELECT DATABASE()
1678074229807|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT SUBSTRING_INDEX(USER(),'@',1)|SELECT SUBSTRING_INDEX(USER(),'@',1)
1678074229810|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT version()|SELECT version()
1678074229890|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1678074229893|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1678074229970|3|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1678074229972|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1678074229979|5|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT GET_LOCK(?,10)|SELECT GET_LOCK('Flyway-205212893',10)
1678074229995|2|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1678074229997|1|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1678074230008|6|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`|SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `flyway`.`flyway_schema_history` WHERE `installed_rank` > -1 ORDER BY `installed_rank`
1678074230018|4|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT RELEASE_LOCK('Flyway-205212893')|SELECT RELEASE_LOCK('Flyway-205212893')
1678074230025|6|statement|connection 0|url ******************************************************************************************************************************************************************************|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL|SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL
1678074230030|4|statement|connection 0|url ******************************************************************************************************************************************************************************|SET foreign_key_checks=?, sql_safe_updates=?|SET foreign_key_checks=1, sql_safe_updates=0
1678074230041|0|commit|connection 0|url ******************************************************************************************************************************************************************************||
